package org.npci.rustyclient.client.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for handling network compatibility issues across different platforms,
 * particularly addressing Linux-specific gRPC connection problems.
 */
public class NetworkCompatibilityUtils {
    private static final Logger logger = LoggerFactory.getLogger(NetworkCompatibilityUtils.class);
    
    private static final String OS_NAME = System.getProperty("os.name", "").toLowerCase();
    private static final boolean IS_LINUX = OS_NAME.contains("linux");
    private static final boolean IS_WINDOWS = OS_NAME.contains("windows");
    private static final boolean IS_MAC = OS_NAME.contains("mac");
    
    /**
     * Configure JVM system properties for optimal gRPC networking on Linux.
     * This method should be called early in the application lifecycle,
     * preferably in a static initializer or main method.
     */
    public static void configureLinuxNetworking() {
        logger.info("Configuring JVM networking properties for compatibility (OS: {})", OS_NAME);

        // Apply these settings regardless of OS for maximum compatibility
        // Prefer IPv4 stack to avoid IPv6 address resolution issues
        System.setProperty("java.net.preferIPv4Stack", "true");

        // Prefer IPv4 addresses when both IPv4 and IPv6 are available
        System.setProperty("java.net.preferIPv6Addresses", "false");

        // Configure DNS cache settings for better reliability
        System.setProperty("networkaddress.cache.ttl", "30");
        System.setProperty("networkaddress.cache.negative.ttl", "10");

        // Configure socket options for better compatibility
        System.setProperty("java.net.useSystemProxies", "false");

        // Additional properties for gRPC/Netty compatibility
        System.setProperty("io.netty.tryReflectionSetAccessible", "true");
        System.setProperty("io.netty.noUnsafe", "false");

        logger.info("Network compatibility configuration applied successfully");
        logger.info("  java.net.preferIPv4Stack: {}", System.getProperty("java.net.preferIPv4Stack"));
        logger.info("  java.net.preferIPv6Addresses: {}", System.getProperty("java.net.preferIPv6Addresses"));
    }

    /**
     * Force configure networking properties - use this if the regular method doesn't work.
     * This method applies settings regardless of the operating system.
     */
    public static void forceConfigureNetworking() {
        logger.warn("Force configuring networking properties - this should only be used if regular configuration fails");

        // Set all properties that might help with address type exceptions
        System.setProperty("java.net.preferIPv4Stack", "true");
        System.setProperty("java.net.preferIPv6Addresses", "false");
        System.setProperty("networkaddress.cache.ttl", "0");  // Disable caching
        System.setProperty("networkaddress.cache.negative.ttl", "0");
        System.setProperty("java.net.useSystemProxies", "false");

        // Netty-specific properties
        System.setProperty("io.netty.tryReflectionSetAccessible", "true");
        System.setProperty("io.netty.noUnsafe", "false");
        System.setProperty("io.netty.noKeySetOptimization", "true");

        // Additional JVM networking properties
        System.setProperty("sun.net.useExclusiveBind", "false");

        logger.warn("Force network configuration applied - check if this resolves the UnsupportedAddressTypeException");
    }
    
    /**
     * Get platform-specific recommendations for RustyCluster client configuration.
     *
     * @return A string with platform-specific recommendations
     */
    public static String getPlatformRecommendations() {
        StringBuilder recommendations = new StringBuilder();
        recommendations.append("Platform: ").append(OS_NAME).append("\n");
        
        if (IS_LINUX) {
            recommendations.append("Linux-specific recommendations:\n");
            recommendations.append("- Call NetworkCompatibilityUtils.configureLinuxNetworking() early in your application\n");
            recommendations.append("- Use preferIpv4(true) in your client configuration\n");
            recommendations.append("- Consider using IP addresses instead of hostnames if possible\n");
            recommendations.append("- Monitor for UnsupportedAddressTypeException and check IPv6 configuration\n");
        } else if (IS_WINDOWS) {
            recommendations.append("Windows-specific recommendations:\n");
            recommendations.append("- Default configuration should work well\n");
            recommendations.append("- Consider firewall settings if connection issues occur\n");
        } else if (IS_MAC) {
            recommendations.append("macOS-specific recommendations:\n");
            recommendations.append("- Default configuration should work well\n");
            recommendations.append("- Check network interface configuration if issues occur\n");
        }
        
        return recommendations.toString();
    }
    
    /**
     * Check if the current platform is Linux.
     *
     * @return true if running on Linux
     */
    public static boolean isLinux() {
        return IS_LINUX;
    }
    
    /**
     * Check if the current platform is Windows.
     *
     * @return true if running on Windows
     */
    public static boolean isWindows() {
        return IS_WINDOWS;
    }
    
    /**
     * Check if the current platform is macOS.
     *
     * @return true if running on macOS
     */
    public static boolean isMac() {
        return IS_MAC;
    }
    
    /**
     * Log current network-related system properties for debugging.
     */
    public static void logNetworkProperties() {
        logger.info("Network System Properties:");
        logger.info("  os.name: {}", System.getProperty("os.name"));
        logger.info("  java.version: {}", System.getProperty("java.version"));
        logger.info("  java.net.preferIPv4Stack: {}", System.getProperty("java.net.preferIPv4Stack"));
        logger.info("  java.net.preferIPv6Addresses: {}", System.getProperty("java.net.preferIPv6Addresses"));
        logger.info("  networkaddress.cache.ttl: {}", System.getProperty("networkaddress.cache.ttl"));
        logger.info("  networkaddress.cache.negative.ttl: {}", System.getProperty("networkaddress.cache.negative.ttl"));
    }
    
    /**
     * Validate that the system is properly configured for gRPC networking.
     *
     * @return true if the configuration looks good, false if there might be issues
     */
    public static boolean validateNetworkConfiguration() {
        boolean isValid = true;
        
        if (IS_LINUX) {
            String preferIPv4Stack = System.getProperty("java.net.preferIPv4Stack");
            String preferIPv6Addresses = System.getProperty("java.net.preferIPv6Addresses");
            
            if (!"true".equals(preferIPv4Stack)) {
                logger.warn("For Linux compatibility, consider setting java.net.preferIPv4Stack=true");
                isValid = false;
            }
            
            if ("true".equals(preferIPv6Addresses)) {
                logger.warn("For Linux compatibility, consider setting java.net.preferIPv6Addresses=false");
                isValid = false;
            }
        }
        
        return isValid;
    }
}
