package org.npci.rustyclient.client.connection;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;
import io.grpc.okhttp.OkHttpChannelBuilder;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

/**
 * Factory for creating and caching gRPC channels to RustyCluster nodes.
 * Implements channel reuse optimization for better performance and resource utilization.
 * Includes Linux compatibility fixes for address resolution issues.
 */
public class GrpcChannelFactory {
    private static final Logger logger = LoggerFactory.getLogger(GrpcChannelFactory.class);

    private final RustyClusterClientConfig config;
    private final ConcurrentHashMap<String, ManagedChannel> channelCache = new ConcurrentHashMap<>();

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
    public GrpcChannelFactory(RustyClusterClientConfig config) {
        this.config = config;
    }

    /**
     * Get or create a gRPC channel for the given node.
     * Implements channel reuse optimization for better performance.
     *
     * @param nodeConfig The node configuration
     * @return A cached or new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
        return channelCache.computeIfAbsent(nodeConfig.getAddress(),
            address -> {
                if (config.isUseSecureConnection()) {
                    return createSecureChannel(nodeConfig);
                } else {
                    return createInsecureChannel(nodeConfig);
                }
            });
    }

    /**
     * Close all cached channels and clear the cache.
     * Should be called when the factory is no longer needed.
     */
    public void close() {
        channelCache.values().forEach(channel -> {
            try {
                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        channelCache.clear();
    }

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
        // Resolve address to handle IPv6/IPv4 compatibility issues on Linux
        InetSocketAddress resolvedAddress = resolveAddress(nodeConfig);

        logger.debug("Creating insecure channel to {}:{} (resolved: {})",
                    nodeConfig.host(), nodeConfig.port(), resolvedAddress);

        return NettyChannelBuilder.forAddress(resolvedAddress.getHostString(), resolvedAddress.getPort())
                .usePlaintext()
                // High-throughput keep-alive settings
                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                .keepAliveWithoutCalls(true)
                // Performance optimizations
                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                // Connection management
                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                // Retry configuration
                .enableRetry()
                .maxRetryAttempts(3)
                .build();
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
            // Resolve address to handle IPv6/IPv4 compatibility issues on Linux
            InetSocketAddress resolvedAddress = resolveAddress(nodeConfig);

            logger.debug("Creating secure channel to {}:{} (resolved: {})",
                        nodeConfig.host(), nodeConfig.port(), resolvedAddress);

            SslContext sslContext = GrpcSslContexts.forClient()
                    .trustManager(new File(config.getTlsCertPath()))
                    .build();

            return NettyChannelBuilder.forAddress(resolvedAddress.getHostString(), resolvedAddress.getPort())
                    .sslContext(sslContext)
                    // High-throughput keep-alive settings
                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive
                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection
                    .keepAliveWithoutCalls(true)
                    // Performance optimizations
                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches
                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata
                    // Connection management
                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections
                    // Retry configuration
                    .enableRetry()
                    .maxRetryAttempts(3)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create secure channel", e);
        }
    }

    /**
     * Resolve the node address to handle IPv6/IPv4 compatibility issues on Linux.
     * This method ensures that we use IPv4 addresses when possible to avoid
     * UnsupportedAddressTypeException on Linux systems.
     *
     * @param nodeConfig The node configuration
     * @return A resolved InetSocketAddress
     */
    private InetSocketAddress resolveAddress(NodeConfig nodeConfig) {
        try {
            String host = nodeConfig.host();
            int port = nodeConfig.port();

            logger.debug("Resolving address for {}:{} (preferIpv4: {})", host, port, config.isPreferIpv4());

            // If the host is already an IP address, use it directly but safely
            if (isIpAddress(host)) {
                logger.debug("Using direct IP address: {}", host);
                return createSafeSocketAddress(host, port);
            }

            // Resolve hostname and prefer IPv4 addresses if configured
            InetAddress[] addresses = InetAddress.getAllByName(host);
            logger.debug("Resolved {} to {} addresses", host, addresses.length);

            // If preferIpv4 is enabled, try to find an IPv4 address first
            if (config.isPreferIpv4()) {
                for (InetAddress address : addresses) {
                    if (address instanceof java.net.Inet4Address) {
                        logger.debug("Found IPv4 address for {}: {}", host, address.getHostAddress());
                        return createSafeSocketAddress(address, port);
                    }
                }
                logger.debug("No IPv4 addresses found for {}, trying other addresses", host);
            }

            // If no IPv4 address found or preferIpv4 is false, use the first available address
            if (addresses.length > 0) {
                InetAddress address = addresses[0];
                logger.debug("Using first available address for {}: {} ({})",
                           host, address.getHostAddress(),
                           address instanceof java.net.Inet6Address ? "IPv6" : "IPv4");
                return createSafeSocketAddress(address, port);
            }

            // Fallback to original host if resolution fails
            logger.warn("Could not resolve any addresses for {}, using original hostname", host);
            return createSafeSocketAddress(host, port);

        } catch (Exception e) {
            logger.error("Failed to resolve hostname {}: {} - {}", nodeConfig.host(), e.getClass().getSimpleName(), e.getMessage());
            // Last resort - try to create with original host
            return createSafeSocketAddress(nodeConfig.host(), nodeConfig.port());
        }
    }

    /**
     * Safely create an InetSocketAddress, handling potential UnsupportedAddressTypeException.
     */
    private InetSocketAddress createSafeSocketAddress(String host, int port) {
        try {
            return new InetSocketAddress(host, port);
        } catch (Exception e) {
            logger.error("Failed to create socket address for {}:{} - {} - {}",
                        host, port, e.getClass().getSimpleName(), e.getMessage());
            // Try with localhost as last resort
            if (!"127.0.0.1".equals(host)) {
                logger.warn("Falling back to 127.0.0.1:{} due to address creation failure", port);
                return new InetSocketAddress("127.0.0.1", port);
            }
            throw new RuntimeException("Cannot create socket address for " + host + ":" + port, e);
        }
    }

    /**
     * Safely create an InetSocketAddress from an InetAddress.
     */
    private InetSocketAddress createSafeSocketAddress(InetAddress address, int port) {
        try {
            return new InetSocketAddress(address, port);
        } catch (Exception e) {
            logger.error("Failed to create socket address for {}:{} - {} - {}",
                        address.getHostAddress(), port, e.getClass().getSimpleName(), e.getMessage());
            // Try with the string representation
            return createSafeSocketAddress(address.getHostAddress(), port);
        }
    }

    /**
     * Check if a string represents an IP address (IPv4 or IPv6).
     *
     * @param host The host string to check
     * @return true if the host is an IP address, false otherwise
     */
    private boolean isIpAddress(String host) {
        try {
            InetAddress.getByName(host);
            // If it contains dots or colons, it's likely an IP address
            return host.contains(".") || host.contains(":");
        } catch (UnknownHostException e) {
            return false;
        }
    }
}
