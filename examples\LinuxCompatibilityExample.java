import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.util.NetworkCompatibilityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Example demonstrating how to configure RustyCluster client for Linux compatibility.
 * This example addresses the UnsupportedAddressTypeException that can occur on Linux systems.
 */
public class LinuxCompatibilityExample {
    private static final Logger logger = LoggerFactory.getLogger(LinuxCompatibilityExample.class);

    public static void main(String[] args) {
        // IMPORTANT: Configure Linux networking BEFORE creating any clients
        // This should be done early in your application startup
        NetworkCompatibilityUtils.configureLinuxNetworking();
        
        // Log current platform and recommendations
        logger.info("Platform recommendations:\n{}", NetworkCompatibilityUtils.getPlatformRecommendations());
        
        // Log current network properties for debugging
        NetworkCompatibilityUtils.logNetworkProperties();
        
        // Validate network configuration
        if (!NetworkCompatibilityUtils.validateNetworkConfiguration()) {
            logger.warn("Network configuration may cause issues on this platform");
        }

        // Configure the client with Linux-compatible settings
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                // Use IP addresses instead of hostnames when possible
                .addPrimaryNode("127.0.0.1", 50051)    // Instead of "localhost"
                .addSecondaryNode("*************", 50051)  // Instead of hostname
                
                // Enable IPv4 preference (default is true, but explicit for clarity)
                .preferIpv4(true)
                
                // Optional: Authentication
                .authentication("admin", "password")
                
                // Timeout settings - shorter timeouts help detect connection issues faster
                .connectionTimeout(3, TimeUnit.SECONDS)
                .readTimeout(2, TimeUnit.SECONDS)
                .writeTimeout(2, TimeUnit.SECONDS)
                
                // Retry settings - fewer retries for faster failure detection
                .maxRetries(2)
                .retryDelay(100, TimeUnit.MILLISECONDS)
                
                // Connection pool settings
                .maxConnectionsPerNode(10)
                
                // Enable failback for high availability
                .enableFailback(true)
                .failbackCheckInterval(30, TimeUnit.SECONDS)
                
                .build();

        // Alternative configuration using hostnames (if IP addresses are not available)
        RustyClusterClientConfig hostnameConfig = createHostnameBasedConfig();

        // Test both configurations
        testConfiguration("IP-based configuration", config);
        testConfiguration("Hostname-based configuration", hostnameConfig);
    }

    private static RustyClusterClientConfig createHostnameBasedConfig() {
        return RustyClusterClientConfig.builder()
                // When using hostnames, ensure preferIpv4 is enabled
                .addPrimaryNode("localhost", 50051)
                .addSecondaryNode("backup-server", 50051)
                
                // Critical: Enable IPv4 preference for hostname resolution
                .preferIpv4(true)
                
                // Use balanced preset for general reliability
                .balancedPreset()
                
                .build();
    }

    private static void testConfiguration(String configName, RustyClusterClientConfig config) {
        logger.info("Testing {} ...", configName);
        
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Test basic operations
            testBasicOperations(client);
            logger.info("{} - SUCCESS", configName);
            
        } catch (Exception e) {
            logger.error("{} - FAILED: {}", configName, e.getMessage());
            
            // Provide specific guidance for common Linux issues
            if (e.getCause() instanceof java.nio.channels.UnsupportedAddressTypeException) {
                logger.error("UnsupportedAddressTypeException detected. This is a common Linux issue.");
                logger.error("Solutions:");
                logger.error("1. Ensure NetworkCompatibilityUtils.configureLinuxNetworking() is called early");
                logger.error("2. Use IP addresses instead of hostnames in your configuration");
                logger.error("3. Check your /etc/hosts file for IPv6 entries");
                logger.error("4. Verify your network interface configuration");
                logger.error("5. Consider disabling IPv6 if not needed: echo 'net.ipv6.conf.all.disable_ipv6 = 1' >> /etc/sysctl.conf");
            }
        }
    }

    private static void testBasicOperations(RustyClusterClient client) throws Exception {
        // Test authentication (if configured)
        if (client.getConfig().hasAuthentication()) {
            logger.info("Testing authentication...");
            client.authenticate();
            logger.info("Authentication successful");
        }

        // Test basic set/get operations
        logger.info("Testing basic operations...");
        
        String testKey = "linux_test_key";
        String testValue = "linux_test_value_" + System.currentTimeMillis();
        
        // Set operation
        boolean setResult = client.set(testKey, testValue);
        if (!setResult) {
            throw new RuntimeException("Set operation failed");
        }
        logger.info("Set operation successful: {} = {}", testKey, testValue);
        
        // Get operation
        String retrievedValue = client.get(testKey);
        if (!testValue.equals(retrievedValue)) {
            throw new RuntimeException("Get operation failed: expected " + testValue + ", got " + retrievedValue);
        }
        logger.info("Get operation successful: {} = {}", testKey, retrievedValue);
        
        // Delete operation
        boolean deleteResult = client.delete(testKey);
        if (!deleteResult) {
            throw new RuntimeException("Delete operation failed");
        }
        logger.info("Delete operation successful: {}", testKey);
        
        // Verify deletion
        String deletedValue = client.get(testKey);
        if (deletedValue != null) {
            throw new RuntimeException("Key was not properly deleted");
        }
        logger.info("Deletion verification successful");
    }

    /**
     * Static initializer to configure networking as early as possible.
     * This is an alternative to calling configureLinuxNetworking() in main().
     */
    static {
        // Uncomment the following line to configure networking in static initializer
        // NetworkCompatibilityUtils.configureLinuxNetworking();
    }
}
