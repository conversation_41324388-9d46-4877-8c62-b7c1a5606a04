import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.HScanResult;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Example demonstrating the new methods added to RustyClusterClient:
 * - hDel: Delete hash fields
 * - hScan: Scan hash fields with pagination
 * - hLen: Get hash length
 * - sAdd: Add members to a set
 * - sMembers: Get all members of a set
 * - delMultiple: Delete multiple keys
 */
public class RustyClusterClientNewMethodsExample {

    public static void main(String[] args) {
        System.out.println("=== RustyCluster Client New Methods Example ===");

        // Configure the client
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .addSecondaryNode("localhost", 50052)
                .authentication("admin", "password")
                .enableFailback(true)
                .failbackCheckInterval(30, TimeUnit.SECONDS)
                .build();

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Demonstrate hash operations
            demonstrateHashOperations(client);

            // Demonstrate set operations
            demonstrateSetOperations(client);

            // Demonstrate multiple key deletion
            demonstrateMultipleKeyDeletion(client);

            // Demonstrate async operations
            demonstrateAsyncOperations(client);

        } catch (Exception e) {
            System.err.println("Error in example: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Demonstrate new hash operations: hDel, hScan, hLen
     */
    private static void demonstrateHashOperations(RustyClusterClient client) {
        System.out.println("\n--- Hash Operations ---");

        String hashKey = "user:1001:profile";

        try {
            // Set up test data
            client.hSet(hashKey, "name", "John Doe");
            client.hSet(hashKey, "email", "<EMAIL>");
            client.hSet(hashKey, "age", "30");
            client.hSet(hashKey, "city", "New York");
            client.hSet(hashKey, "country", "USA");
            client.hSet(hashKey, "phone", "******-0123");

            // 1. hLen - Get hash length
            int hashLength = client.hLen(hashKey);
            System.out.println("Hash length: " + hashLength);

            // 2. hScan - Scan hash fields with pagination
            System.out.println("\nScanning hash fields:");
            long cursor = 0;
            int totalFields = 0;
            do {
                HScanResult result = client.hScan(hashKey, cursor, null, 2); // Get 2 fields at a time
                cursor = result.getNextCursor();
                Map<String, String> fields = result.getFields();
                
                System.out.println("Cursor: " + cursor + ", Fields: " + fields);
                totalFields += fields.size();
            } while (cursor != 0);
            System.out.println("Total fields scanned: " + totalFields);

            // 3. hScan with pattern matching
            System.out.println("\nScanning hash fields with pattern '*e*':");
            HScanResult patternResult = client.hScan(hashKey, 0, "*e*", null);
            System.out.println("Fields matching pattern: " + patternResult.getFields());

            // 4. hDel - Delete specific hash fields
            int deletedCount = client.hDel(hashKey, "phone", "city");
            System.out.println("Deleted " + deletedCount + " fields");

            // Verify deletion
            int newLength = client.hLen(hashKey);
            System.out.println("Hash length after deletion: " + newLength);

            // Clean up
            client.delete(hashKey);

        } catch (Exception e) {
            System.err.println("Error in hash operations: " + e.getMessage());
        }
    }

    /**
     * Demonstrate new set operations: sAdd, sMembers
     */
    private static void demonstrateSetOperations(RustyClusterClient client) {
        System.out.println("\n--- Set Operations ---");

        String setKey = "user:1001:tags";

        try {
            // 1. sAdd - Add members to a set
            int addedCount1 = client.sAdd(setKey, "developer", "java", "backend");
            System.out.println("Added " + addedCount1 + " members to set");

            // Add more members (some duplicates)
            int addedCount2 = client.sAdd(setKey, "java", "spring", "microservices", "redis");
            System.out.println("Added " + addedCount2 + " more members to set (duplicates ignored)");

            // 2. sMembers - Get all members of a set
            List<String> members = client.sMembers(setKey);
            System.out.println("Set members: " + members);
            System.out.println("Total unique members: " + members.size());

            // Clean up
            client.delete(setKey);

        } catch (Exception e) {
            System.err.println("Error in set operations: " + e.getMessage());
        }
    }

    /**
     * Demonstrate multiple key deletion: delMultiple
     */
    private static void demonstrateMultipleKeyDeletion(RustyClusterClient client) {
        System.out.println("\n--- Multiple Key Deletion ---");

        try {
            // Set up test data
            client.set("temp:key1", "value1");
            client.set("temp:key2", "value2");
            client.set("temp:key3", "value3");
            client.set("temp:key4", "value4");
            client.set("temp:key5", "value5");

            // Verify keys exist
            System.out.println("Key1 exists: " + client.exists("temp:key1"));
            System.out.println("Key3 exists: " + client.exists("temp:key3"));
            System.out.println("Key5 exists: " + client.exists("temp:key5"));

            // Delete multiple keys at once
            int deletedCount = client.delMultiple("temp:key1", "temp:key3", "temp:key5", "temp:nonexistent");
            System.out.println("Deleted " + deletedCount + " keys");

            // Verify deletion
            System.out.println("Key1 exists after deletion: " + client.exists("temp:key1"));
            System.out.println("Key2 exists after deletion: " + client.exists("temp:key2"));
            System.out.println("Key3 exists after deletion: " + client.exists("temp:key3"));

            // Clean up remaining keys
            client.delMultiple("temp:key2", "temp:key4");

        } catch (Exception e) {
            System.err.println("Error in multiple key deletion: " + e.getMessage());
        }
    }

    /**
     * Demonstrate async versions of the new methods
     */
    private static void demonstrateAsyncOperations(RustyClusterClient client) {
        System.out.println("\n--- Async Operations ---");

        try {
            String hashKey = "async:user:profile";
            String setKey = "async:user:tags";

            // Async hash operations
            CompletableFuture<Void> hashOps = client.hSetAsync(hashKey, "name", "Jane Doe")
                .thenCompose(result -> client.hSetAsync(hashKey, "email", "<EMAIL>"))
                .thenCompose(result -> client.hSetAsync(hashKey, "age", "25"))
                .thenCompose(result -> client.hLenAsync(hashKey))
                .thenAccept(length -> System.out.println("Async hash length: " + length))
                .thenCompose(v -> client.hScanAsync(hashKey, 0))
                .thenAccept(result -> System.out.println("Async hash scan: " + result.getFields()))
                .thenCompose(v -> client.hDelAsync(hashKey, "age"))
                .thenAccept(deleted -> System.out.println("Async deleted " + deleted + " hash fields"));

            // Async set operations
            CompletableFuture<Void> setOps = client.sAddAsync(setKey, "async", "java", "reactive")
                .thenAccept(added -> System.out.println("Async added " + added + " set members"))
                .thenCompose(v -> client.sMembersAsync(setKey))
                .thenAccept(members -> System.out.println("Async set members: " + members));

            // Wait for all async operations to complete
            CompletableFuture.allOf(hashOps, setOps).join();

            // Async cleanup
            client.delMultipleAsync(hashKey, setKey)
                .thenAccept(deleted -> System.out.println("Async deleted " + deleted + " keys"))
                .join();

            System.out.println("All async operations completed successfully");

        } catch (Exception e) {
            System.err.println("Error in async operations: " + e.getMessage());
        }
    }
}
