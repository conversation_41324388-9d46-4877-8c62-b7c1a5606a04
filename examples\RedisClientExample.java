import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeRole;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Example usage of the RedisClient.
 */
public class RedisClientExample {

    public static void main(String[] args) {
        // Example 1: Basic Redis client configuration
        basicRedisClientExample();

        // Example 2: Redis client with multiple nodes and authentication
        multiNodeRedisClientExample();

        // Example 3: High-throughput Redis client
        highThroughputRedisClientExample();

        // Example 4: Redis cluster mode
        redisClusterExample();

        // Example 5: Lua script operations
        luaScriptExample();

        // Example 6: Async operations
        asyncOperationsExample();
    }

    /**
     * Basic Redis client example with single node.
     */
    public static void basicRedisClientExample() {
        System.out.println("=== Basic Redis Client Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .build();

        try (RedisClient client = new RedisClient(config)) {
            // String operations
            client.set("user:1001", "John Doe");
            String user = client.get("user:1001");
            System.out.println("Retrieved user: " + user);

            // Hash operations
            client.hSet("user:1001:profile", "name", "John Doe");
            client.hSet("user:1001:profile", "email", "<EMAIL>");
            client.hSet("user:1001:profile", "age", "30");

            Map<String, String> profile = client.hGetAll("user:1001:profile");
            System.out.println("User profile: " + profile);

            // Hash increment operations
            client.hSet("user:1001:stats", "login_count", "0");
            long loginCount = client.hIncrBy("user:1001:stats", "login_count", 1);
            System.out.println("Login count after increment: " + loginCount);

            client.hSet("user:1001:stats", "score", "0.0");
            double newScore = client.hIncrByFloat("user:1001:stats", "score", 10.5);
            System.out.println("Score after increment: " + newScore);

            // Cleanup
            client.delete("user:1001");
            client.delete("user:1001:profile");
            client.delete("user:1001:stats");

        } catch (Exception e) {
            System.err.println("Error in basic example: " + e.getMessage());
        }
    }

    /**
     * Multi-node Redis client with authentication.
     */
    public static void multiNodeRedisClientExample() {
        System.out.println("\n=== Multi-Node Redis Client Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addPrimaryNode("redis-primary.example.com", 6379)
                .addSecondaryNode("redis-secondary.example.com", 6379)
                .addNode("redis-tertiary.example.com", 6379, RedisNodeRole.TERTIARY)
                .authentication("redis-user", "redis-password")
                .database(1)
                .maxConnectionsPerNode(20)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .build();

        try (RedisClient client = new RedisClient(config)) {
            // Test connectivity
            if (client.ping()) {
                System.out.println("Successfully connected to Redis cluster");

                // Perform operations
                client.set("session:abc123", "user_data");
                client.setEx("temp:token", "temp_value", 300); // 5 minutes TTL

                // Hash operations for user session
                Map<String, String> sessionData = new HashMap<>();
                sessionData.put("user_id", "1001");
                sessionData.put("login_time", String.valueOf(System.currentTimeMillis()));
                sessionData.put("ip_address", "*************");

                client.hMSet("session:abc123:data", sessionData);

                System.out.println("Session data stored successfully");
            } else {
                System.err.println("Failed to connect to Redis cluster");
            }

        } catch (Exception e) {
            System.err.println("Error in multi-node example: " + e.getMessage());
        }
    }

    /**
     * High-throughput Redis client example.
     */
    public static void highThroughputRedisClientExample() {
        System.out.println("\n=== High-Throughput Redis Client Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addNodes("localhost:6379", "localhost:6380", "localhost:6381")
                .highThroughputPreset()
                .maxConnectionsPerNode(50)
                .build();

        try (RedisClient client = new RedisClient(config)) {
            System.out.println("High-throughput Redis client initialized");
            System.out.println("Pool stats: " + client.getPoolStats());

            // Simulate high-throughput operations
            long startTime = System.currentTimeMillis();
            int operations = 1000;

            for (int i = 0; i < operations; i++) {
                String key = "ht:key:" + i;
                String value = "value:" + i;
                client.set(key, value);

                if (i % 100 == 0) {
                    System.out.println("Completed " + i + " operations");
                }
            }

            long endTime = System.currentTimeMillis();
            double opsPerSecond = (operations * 1000.0) / (endTime - startTime);
            System.out.println("Completed " + operations + " operations in " + 
                             (endTime - startTime) + "ms");
            System.out.println("Operations per second: " + String.format("%.2f", opsPerSecond));

        } catch (Exception e) {
            System.err.println("Error in high-throughput example: " + e.getMessage());
        }
    }

    /**
     * Redis cluster mode example.
     */
    public static void redisClusterExample() {
        System.out.println("\n=== Redis Cluster Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addNodes("redis-cluster-1:7000", "redis-cluster-2:7000", "redis-cluster-3:7000")
                .enableClusterMode()
                .maxRedirections(5)
                .enableReadFromReplicas()
                .build();

        try (RedisClient client = new RedisClient(config)) {
            System.out.println("Redis cluster client initialized");

            // Cluster operations
            client.set("cluster:key1", "cluster_value1");
            client.set("cluster:key2", "cluster_value2");

            String value1 = client.get("cluster:key1");
            String value2 = client.get("cluster:key2");

            System.out.println("Cluster value 1: " + value1);
            System.out.println("Cluster value 2: " + value2);

        } catch (Exception e) {
            System.err.println("Error in cluster example: " + e.getMessage());
        }
    }

    /**
     * Lua script operations example.
     */
    public static void luaScriptExample() {
        System.out.println("\n=== Lua Script Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .build();

        try (RedisClient client = new RedisClient(config)) {
            // Example 1: Simple script that returns a value
            String simpleScript = "return 'Hello from Lua!'";
            String sha1 = client.scriptLoad(simpleScript);
            if (sha1 != null) {
                System.out.println("Loaded script with SHA: " + sha1);
                Object result = client.evalSha(sha1);
                System.out.println("Script result: " + result);
            }

            // Example 2: Script with keys and arguments
            String incrementScript = """
                local key = KEYS[1]
                local increment = tonumber(ARGV[1])
                local current = redis.call('GET', key)
                if current == false then
                    current = 0
                else
                    current = tonumber(current)
                end
                local new_value = current + increment
                redis.call('SET', key, new_value)
                return new_value
                """;

            String sha2 = client.scriptLoad(incrementScript);
            if (sha2 != null) {
                System.out.println("Loaded increment script with SHA: " + sha2);

                // Execute script with key and argument
                Object result = client.evalSha(sha2, List.of("counter:script"), List.of("5"));
                System.out.println("Counter after increment: " + result);

                // Execute again to see increment
                result = client.evalSha(sha2, List.of("counter:script"), List.of("3"));
                System.out.println("Counter after second increment: " + result);
            }

            // Example 3: Direct script execution (without pre-loading)
            String directScript = """
                local key = KEYS[1]
                local field = KEYS[2]
                local value = ARGV[1]
                redis.call('HSET', key, field, value)
                return redis.call('HGET', key, field)
                """;

            Object directResult = client.eval(directScript,
                List.of("script:hash", "test_field"),
                List.of("test_value"));
            System.out.println("Direct script result: " + directResult);

            // Cleanup
            client.delete("counter:script");
            client.delete("script:hash");

            System.out.println("Lua script operations completed");

        } catch (Exception e) {
            System.err.println("Error in Lua script example: " + e.getMessage());
        }
    }

    /**
     * Asynchronous operations example.
     */
    public static void asyncOperationsExample() {
        System.out.println("\n=== Async Operations Example ===");

        RedisClientConfig config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .build();

        try (RedisClient client = new RedisClient(config)) {
            // Async string operations
            CompletableFuture<Boolean> setFuture1 = client.setAsync("async:key1", "async_value1");
            CompletableFuture<Boolean> setFuture2 = client.setAsync("async:key2", "async_value2");
            CompletableFuture<Boolean> setFuture3 = client.setAsync("async:key3", "async_value3");

            // Wait for all sets to complete
            CompletableFuture.allOf(setFuture1, setFuture2, setFuture3).join();
            System.out.println("All async set operations completed");

            // Async get operations
            CompletableFuture<String> getFuture1 = client.getAsync("async:key1");
            CompletableFuture<String> getFuture2 = client.getAsync("async:key2");
            CompletableFuture<String> getFuture3 = client.getAsync("async:key3");

            // Process results as they complete
            getFuture1.thenAccept(value -> System.out.println("Async key1: " + value));
            getFuture2.thenAccept(value -> System.out.println("Async key2: " + value));
            getFuture3.thenAccept(value -> System.out.println("Async key3: " + value));

            // Wait for all gets to complete
            CompletableFuture.allOf(getFuture1, getFuture2, getFuture3).join();

            // Async hash operations
            CompletableFuture<Boolean> hSetFuture = client.hSetAsync("async:hash", "field1", "value1");
            hSetFuture.thenCompose(result -> {
                if (result) {
                    return client.hGetAsync("async:hash", "field1");
                } else {
                    return CompletableFuture.completedFuture(null);
                }
            }).thenAccept(value -> {
                System.out.println("Async hash field1: " + value);
            }).join();

            // Async hash increment operations
            client.hSetAsync("async:hash", "counter", "0")
                .thenCompose(result -> client.hIncrByAsync("async:hash", "counter", 5))
                .thenAccept(newValue -> System.out.println("Async hash counter: " + newValue))
                .join();

            // Async script operations
            String script = "return 'Hello from async Lua!'";
            client.scriptLoadAsync(script)
                .thenCompose(sha -> {
                    if (sha != null) {
                        return client.evalShaAsync(sha);
                    } else {
                        return CompletableFuture.completedFuture(null);
                    }
                })
                .thenAccept(result -> System.out.println("Async script result: " + result))
                .join();

            // Cleanup
            client.deleteAsync("async:key1");
            client.deleteAsync("async:key2");
            client.deleteAsync("async:key3");
            client.deleteAsync("async:hash");

            System.out.println("Async operations example completed");

        } catch (Exception e) {
            System.err.println("Error in async example: " + e.getMessage());
        }
    }
}
