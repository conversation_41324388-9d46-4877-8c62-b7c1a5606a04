import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.HScanResult;
import org.npci.rustyclient.redis.config.RedisClientConfig;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * Comprehensive test example demonstrating ALL Redis client operations.
 * This example shows how to use the RedisClient for all available Redis operations
 * including string operations, hash operations, set operations, script operations,
 * increment/decrement operations, and asynchronous variants of all operations.
 */
public class RedisClientComprehensiveTest {

    public static void main(String[] args) {
        System.out.println("=== Comprehensive Redis Client Test Example ===");

        // Configure the client
        RedisClientConfig config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .authentication("redis-user", "redis-password")  // Optional
                .database(1)  // Optional database selection
                .build();

        // Test all operations systematically
        testStringOperations(config);
        testHashOperations(config);
        testSetOperations(config);
        testIncrementDecrementOperations(config);
        testScriptOperations(config);
        testUtilityOperations(config);
        testAsyncOperations(config);

        System.out.println("=== All Redis Client Tests Completed ===");
    }

    /**
     * Test all string-related operations
     */
    private static void testStringOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing String Operations ---");
        
        try (RedisClient client = new RedisClient(config)) {
            // Basic set/get operations
            System.out.println("Testing basic set/get operations:");
            boolean setResult = client.set("test:string:key1", "value1");
            System.out.println("Set result: " + setResult);
            
            String getValue = client.get("test:string:key1");
            System.out.println("Get result: " + getValue);
            
            // Set with expiration
            System.out.println("\nTesting setEx operation:");
            boolean setExResult = client.setEx("test:string:key2", "value2", 300); // 5 minutes
            System.out.println("SetEx result: " + setExResult);
            
            // Set if not exists
            System.out.println("\nTesting setNX operation:");
            boolean setNXResult1 = client.setNX("test:string:key3", "value3");
            System.out.println("SetNX result (new key): " + setNXResult1);
            
            boolean setNXResult2 = client.setNX("test:string:key3", "value3_updated");
            System.out.println("SetNX result (existing key): " + setNXResult2);
            
            // Exists check
            System.out.println("\nTesting exists operation:");
            boolean existsResult = client.exists("test:string:key1");
            System.out.println("Exists result: " + existsResult);
            
            boolean notExistsResult = client.exists("test:string:nonexistent");
            System.out.println("Not exists result: " + notExistsResult);
            
            // Expire operation
            System.out.println("\nTesting expire operation:");
            boolean expireResult = client.expire("test:string:key1", 600); // 10 minutes
            System.out.println("Expire result: " + expireResult);
            
            // Delete operation
            System.out.println("\nTesting delete operation:");
            boolean deleteResult = client.delete("test:string:key2");
            System.out.println("Delete result: " + deleteResult);
            
            // Multiple key deletion
            System.out.println("\nTesting delMultiple operation:");
            client.set("test:string:multi1", "value1");
            client.set("test:string:multi2", "value2");
            client.set("test:string:multi3", "value3");
            
            long delMultipleResult = client.delMultiple("test:string:multi1", "test:string:multi2", "test:string:multi3");
            System.out.println("DelMultiple result: " + delMultipleResult);
            
        } catch (Exception e) {
            System.err.println("Error in string operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all hash-related operations
     */
    private static void testHashOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Hash Operations ---");
        
        try (RedisClient client = new RedisClient(config)) {
            String hashKey = "test:hash:user:1001";
            
            // Basic hash set/get
            System.out.println("Testing basic hash operations:");
            boolean hSetResult = client.hSet(hashKey, "name", "John Doe");
            System.out.println("HSet result: " + hSetResult);
            
            String hGetResult = client.hGet(hashKey, "name");
            System.out.println("HGet result: " + hGetResult);
            
            // Multiple hash set
            System.out.println("\nTesting hMSet operation:");
            Map<String, String> hashFields = new HashMap<>();
            hashFields.put("email", "<EMAIL>");
            hashFields.put("age", "30");
            hashFields.put("city", "New York");
            
            boolean hMSetResult = client.hMSet(hashKey, hashFields);
            System.out.println("HMSet result: " + hMSetResult);
            
            // Get all hash fields
            System.out.println("\nTesting hGetAll operation:");
            Map<String, String> allFields = client.hGetAll(hashKey);
            System.out.println("HGetAll result: " + allFields);
            
            // Hash field exists
            System.out.println("\nTesting hExists operation:");
            boolean hExistsResult = client.hExists(hashKey, "name");
            System.out.println("HExists result: " + hExistsResult);
            
            // Hash increment operations
            System.out.println("\nTesting hash increment operations:");
            client.hSet(hashKey, "counter", "10");
            
            long hIncrResult = client.hIncr(hashKey, "counter");
            System.out.println("HIncr result: " + hIncrResult);
            
            long hIncrByResult = client.hIncrBy(hashKey, "counter", 5);
            System.out.println("HIncrBy result: " + hIncrByResult);
            
            client.hSet(hashKey, "score", "85.5");
            double hIncrByFloatResult = client.hIncrByFloat(hashKey, "score", 2.5);
            System.out.println("HIncrByFloat result: " + hIncrByFloatResult);
            
            // Hash field deletion
            System.out.println("\nTesting hDel operations:");
            boolean hDelSingleResult = client.hDel(hashKey, "city");
            System.out.println("HDel single field result: " + hDelSingleResult);
            
            long hDelMultipleResult = client.hDel(hashKey, "email", "age");
            System.out.println("HDel multiple fields result: " + hDelMultipleResult);
            
            // Hash scan operation
            System.out.println("\nTesting hScan operation:");
            HScanResult hScanResult = client.hScan(hashKey, "0");
            System.out.println("HScan result - cursor: " + hScanResult.getNextCursor() + 
                             ", fields: " + hScanResult.getFields());
            
            // Hash length
            System.out.println("\nTesting hLen operation:");
            long hLenResult = client.hLen(hashKey);
            System.out.println("HLen result: " + hLenResult);
            
            // Cleanup
            client.delete(hashKey);
            
        } catch (Exception e) {
            System.err.println("Error in hash operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all set-related operations
     */
    private static void testSetOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Set Operations ---");
        
        try (RedisClient client = new RedisClient(config)) {
            String setKey = "test:set:tags";
            
            // Add members to set
            System.out.println("Testing sAdd operation:");
            long sAddResult = client.sAdd(setKey, "redis", "cache", "database", "nosql");
            System.out.println("SAdd result: " + sAddResult);
            
            // Get all set members
            System.out.println("\nTesting sMembers operation:");
            Set<String> sMembersResult = client.sMembers(setKey);
            System.out.println("SMembers result: " + sMembersResult);
            
            // Add duplicate members (should return 0)
            System.out.println("\nTesting sAdd with duplicates:");
            long sAddDuplicateResult = client.sAdd(setKey, "redis", "cache");
            System.out.println("SAdd duplicate result: " + sAddDuplicateResult);
            
            // Cleanup
            client.delete(setKey);
            
        } catch (Exception e) {
            System.err.println("Error in set operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test increment and decrement operations
     */
    private static void testIncrementDecrementOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Increment/Decrement Operations ---");
        
        try (RedisClient client = new RedisClient(config)) {
            String counterKey = "test:counter";
            
            // Initialize counter
            client.set(counterKey, "10");
            
            // Increment operations
            System.out.println("Testing increment operations:");
            long incrResult = client.incr(counterKey);
            System.out.println("Incr result: " + incrResult);
            
            long incrByResult = client.incrBy(counterKey, 5);
            System.out.println("IncrBy result: " + incrByResult);
            
            // Decrement operations
            System.out.println("\nTesting decrement operations:");
            long decrResult = client.decr(counterKey);
            System.out.println("Decr result: " + decrResult);
            
            long decrByResult = client.decrBy(counterKey, 3);
            System.out.println("DecrBy result: " + decrByResult);
            
            // Cleanup
            client.delete(counterKey);
            
        } catch (Exception e) {
            System.err.println("Error in increment/decrement operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test script-related operations
     */
    private static void testScriptOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Script Operations ---");
        
        try (RedisClient client = new RedisClient(config)) {
            // Load a simple Lua script
            System.out.println("Testing scriptLoad operation:");
            String script = "return 'Hello from Lua script!'";
            String scriptSha = client.scriptLoad(script);
            System.out.println("ScriptLoad result (SHA): " + scriptSha);
            
            if (scriptSha != null) {
                // Execute script using SHA
                System.out.println("\nTesting evalSha operation:");
                Object evalShaResult = client.evalSha(scriptSha);
                System.out.println("EvalSha result: " + evalShaResult);
                
                // Execute script with keys and args
                System.out.println("\nTesting evalSha with keys and args:");
                String scriptWithArgs = "return {KEYS[1], ARGV[1]}";
                String scriptWithArgsSha = client.scriptLoad(scriptWithArgs);
                
                if (scriptWithArgsSha != null) {
                    Object evalShaWithArgsResult = client.evalSha(scriptWithArgsSha, 
                                                                 Arrays.asList("mykey"), 
                                                                 Arrays.asList("myarg"));
                    System.out.println("EvalSha with args result: " + evalShaWithArgsResult);
                }
            }
            
            // Direct script evaluation
            System.out.println("\nTesting eval operation:");
            Object evalResult = client.eval("return 'Direct script execution!'", 
                                           new ArrayList<>(), new ArrayList<>());
            System.out.println("Eval result: " + evalResult);
            
        } catch (Exception e) {
            System.err.println("Error in script operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test utility operations
     */
    private static void testUtilityOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Utility Operations ---");

        try (RedisClient client = new RedisClient(config)) {
            // Ping operation
            System.out.println("Testing ping operation:");
            boolean pingResult = client.ping();
            System.out.println("Ping result: " + pingResult);

            // Health check
            System.out.println("\nTesting healthCheck operation:");
            boolean healthResult = client.healthCheck();
            System.out.println("Health check result: " + healthResult);

            // Pool statistics
            System.out.println("\nTesting getPoolStats operation:");
            String poolStats = client.getPoolStats();
            System.out.println("Pool stats: " + poolStats);

        } catch (Exception e) {
            System.err.println("Error in utility operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all asynchronous operations
     */
    private static void testAsyncOperations(RedisClientConfig config) {
        System.out.println("\n--- Testing Asynchronous Operations ---");

        try (RedisClient client = new RedisClient(config)) {
            // Async string operations
            System.out.println("Testing async string operations:");

            CompletableFuture<Boolean> setAsyncFuture = client.setAsync("test:async:key1", "async_value1");
            CompletableFuture<Boolean> setExAsyncFuture = client.setExAsync("test:async:key2", "async_value2", 300);

            // Wait for set operations to complete
            CompletableFuture.allOf(setAsyncFuture, setExAsyncFuture).join();
            System.out.println("Async set result: " + setAsyncFuture.join());
            System.out.println("Async setEx result: " + setExAsyncFuture.join());

            // Async get operations
            CompletableFuture<String> getAsyncFuture = client.getAsync("test:async:key1");
            CompletableFuture<Boolean> existsAsyncFuture = client.existsAsync("test:async:key1");

            System.out.println("Async get result: " + getAsyncFuture.join());
            System.out.println("Async exists result: " + existsAsyncFuture.join());

            // Async hash operations
            System.out.println("\nTesting async hash operations:");
            String asyncHashKey = "test:async:hash";

            CompletableFuture<Boolean> hSetAsyncFuture = client.hSetAsync(asyncHashKey, "field1", "value1");
            hSetAsyncFuture.join();
            System.out.println("Async hSet result: " + hSetAsyncFuture.join());

            CompletableFuture<String> hGetAsyncFuture = client.hGetAsync(asyncHashKey, "field1");
            System.out.println("Async hGet result: " + hGetAsyncFuture.join());

            CompletableFuture<Map<String, String>> hGetAllAsyncFuture = client.hGetAllAsync(asyncHashKey);
            System.out.println("Async hGetAll result: " + hGetAllAsyncFuture.join());

            // Async hash increment operations
            client.hSet(asyncHashKey, "counter", "10");
            CompletableFuture<Long> hIncrAsyncFuture = client.hIncrAsync(asyncHashKey, "counter");
            CompletableFuture<Long> hIncrByAsyncFuture = client.hIncrByAsync(asyncHashKey, "counter", 5);

            System.out.println("Async hIncr result: " + hIncrAsyncFuture.join());
            System.out.println("Async hIncrBy result: " + hIncrByAsyncFuture.join());

            client.hSet(asyncHashKey, "score", "75.5");
            CompletableFuture<Double> hIncrByFloatAsyncFuture = client.hIncrByFloatAsync(asyncHashKey, "score", 4.5);
            System.out.println("Async hIncrByFloat result: " + hIncrByFloatAsyncFuture.join());

            // Async hash scan and length
            CompletableFuture<HScanResult> hScanAsyncFuture = client.hScanAsync(asyncHashKey, "0");
            CompletableFuture<Long> hLenAsyncFuture = client.hLenAsync(asyncHashKey);

            System.out.println("Async hScan result: " + hScanAsyncFuture.join());
            System.out.println("Async hLen result: " + hLenAsyncFuture.join());

            // Async hash field deletion
            CompletableFuture<Long> hDelAsyncFuture = client.hDelAsync(asyncHashKey, "field1", "counter");
            System.out.println("Async hDel result: " + hDelAsyncFuture.join());

            // Async set operations
            System.out.println("\nTesting async set operations:");
            String asyncSetKey = "test:async:set";

            CompletableFuture<Long> sAddAsyncFuture = client.sAddAsync(asyncSetKey, "member1", "member2", "member3");
            System.out.println("Async sAdd result: " + sAddAsyncFuture.join());

            CompletableFuture<Set<String>> sMembersAsyncFuture = client.sMembersAsync(asyncSetKey);
            System.out.println("Async sMembers result: " + sMembersAsyncFuture.join());

            // Async script operations
            System.out.println("\nTesting async script operations:");
            String asyncScript = "return 'Async Lua script result!'";
            CompletableFuture<String> scriptLoadAsyncFuture = client.scriptLoadAsync(asyncScript);
            String asyncScriptSha = scriptLoadAsyncFuture.join();
            System.out.println("Async scriptLoad result: " + asyncScriptSha);

            if (asyncScriptSha != null) {
                CompletableFuture<Object> evalShaAsyncFuture = client.evalShaAsync(asyncScriptSha);
                System.out.println("Async evalSha result: " + evalShaAsyncFuture.join());
            }

            CompletableFuture<Object> evalAsyncFuture = client.evalAsync("return 'Direct async eval!'",
                                                                        new ArrayList<>(), new ArrayList<>());
            System.out.println("Async eval result: " + evalAsyncFuture.join());

            // Async utility operations
            System.out.println("\nTesting async utility operations:");
            CompletableFuture<Boolean> pingAsyncFuture = client.pingAsync();
            System.out.println("Async ping result: " + pingAsyncFuture.join());

            // Async cleanup
            System.out.println("\nTesting async cleanup operations:");
            CompletableFuture<Long> delMultipleAsyncFuture = client.delMultipleAsync(
                "test:async:key1", "test:async:key2", asyncHashKey, asyncSetKey);
            System.out.println("Async delMultiple result: " + delMultipleAsyncFuture.join());

            CompletableFuture<Boolean> deleteAsyncFuture = client.deleteAsync("test:async:cleanup");
            System.out.println("Async delete result: " + deleteAsyncFuture.join());

            System.out.println("All async operations completed successfully!");

        } catch (Exception e) {
            System.err.println("Error in async operations: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
