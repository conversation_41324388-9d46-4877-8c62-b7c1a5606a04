package org.npci.rustyclient.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.config.RedisNodeRole;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * Unit tests for RedisClient.
 * Note: These tests require a running Redis server on localhost:6379
 */
class RedisClientTest {

    private RedisClientConfig config;

    @BeforeEach
    void setUp() {
        config = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .maxConnectionsPerNode(5)
                .build();
    }

    @Test
    @DisplayName("Should create RedisClient without errors")
    void shouldCreateRedisClientWithoutErrors() {
        assertDoesNotThrow(() -> {
            try (RedisClient client = new RedisClient(config)) {
                assertThat(client).isNotNull();
            }
        });
    }

    @Test
    @DisplayName("Should build config with multiple nodes")
    void shouldBuildConfigWithMultipleNodes() {
        RedisClientConfig multiNodeConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .addSecondaryNode("localhost", 6380)
                .addNode("localhost", 6381, RedisNodeRole.TERTIARY)
                .build();

        assertThat(multiNodeConfig.getNodes()).hasSize(3);
        assertThat(multiNodeConfig.getNodes().get(0).role()).isEqualTo(RedisNodeRole.PRIMARY);
        assertThat(multiNodeConfig.getNodes().get(1).role()).isEqualTo(RedisNodeRole.SECONDARY);
        assertThat(multiNodeConfig.getNodes().get(2).role()).isEqualTo(RedisNodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should build config with connection strings")
    void shouldBuildConfigWithConnectionStrings() {
        RedisClientConfig stringConfig = RedisClientConfig.builder()
                .addNodes("localhost:6379", "localhost:6380", "localhost:6381")
                .build();

        assertThat(stringConfig.getNodes()).hasSize(3);
        assertThat(stringConfig.getNodes().get(0).role()).isEqualTo(RedisNodeRole.PRIMARY);
        assertThat(stringConfig.getNodes().get(1).role()).isEqualTo(RedisNodeRole.SECONDARY);
        assertThat(stringConfig.getNodes().get(2).role()).isEqualTo(RedisNodeRole.TERTIARY);
    }

    @Test
    @DisplayName("Should build config with authentication")
    void shouldBuildConfigWithAuthentication() {
        RedisClientConfig authConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .authentication("testuser", "testpass")
                .database(1)
                .build();

        assertThat(authConfig.hasAuthentication()).isTrue();
        assertThat(authConfig.getUsername()).isEqualTo("testuser");
        assertThat(authConfig.getPassword()).isEqualTo("testpass");
        assertThat(authConfig.getDatabase()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should build config with high throughput preset")
    void shouldBuildConfigWithHighThroughputPreset() {
        RedisClientConfig htConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .highThroughputPreset()
                .build();

        assertThat(htConfig.getMaxConnectionsPerNode()).isEqualTo(50);
        assertThat(htConfig.getConnectionTimeoutMs()).isEqualTo(1000);
        assertThat(htConfig.getMaxRetries()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should build config with low latency preset")
    void shouldBuildConfigWithLowLatencyPreset() {
        RedisClientConfig llConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 6379)
                .lowLatencyPreset()
                .build();

        assertThat(llConfig.getMaxConnectionsPerNode()).isEqualTo(10);
        assertThat(llConfig.getConnectionTimeoutMs()).isEqualTo(500);
        assertThat(llConfig.getMaxRetries()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should build config with cluster mode")
    void shouldBuildConfigWithClusterMode() {
        RedisClientConfig clusterConfig = RedisClientConfig.builder()
                .addPrimaryNode("localhost", 7000)
                .enableClusterMode()
                .maxRedirections(10)
                .enableReadFromReplicas()
                .build();

        assertThat(clusterConfig.isUseClusterMode()).isTrue();
        assertThat(clusterConfig.getMaxRedirections()).isEqualTo(10);
        assertThat(clusterConfig.isEnableReadFromReplicas()).isTrue();
    }

    @Test
    @DisplayName("Should validate new method signatures exist")
    void shouldValidateNewMethodSignaturesExist() {
        // This test validates that the new methods exist and have correct signatures
        // without requiring a running Redis server

        try (RedisClient client = new RedisClient(config)) {
            // Validate hash increment methods exist
            assertThat(client).isNotNull();

            // These calls will fail without Redis, but we're just checking method signatures
            assertDoesNotThrow(() -> {
                try {
                    client.hIncr("test", "field");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.hIncrBy("test", "field", 5);
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.hIncrByFloat("test", "field", 5.5);
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.scriptLoad("return 'hello'");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.evalSha("sha123", List.of("key1"), List.of("arg1"));
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            // Validate new hash methods exist
            assertDoesNotThrow(() -> {
                try {
                    client.hDel("test", "field1", "field2");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.hLen("test");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.hScan("test", "0", "*pattern*", 10);
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            // Validate new set methods exist
            assertDoesNotThrow(() -> {
                try {
                    client.sAdd("test", "member1", "member2");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            assertDoesNotThrow(() -> {
                try {
                    client.sMembers("test");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            // Validate new key methods exist
            assertDoesNotThrow(() -> {
                try {
                    client.delMultiple("key1", "key2", "key3");
                } catch (Exception ignored) {
                    // Expected without Redis server
                }
            });

            // Validate async methods exist
            assertDoesNotThrow(() -> {
                CompletableFuture<Long> future = client.hIncrAsync("test", "field");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<String> future = client.scriptLoadAsync("return 'hello'");
                assertThat(future).isNotNull();
            });

            // Validate new async methods exist
            assertDoesNotThrow(() -> {
                CompletableFuture<Long> future = client.hDelAsync("test", "field1", "field2");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<Long> future = client.hLenAsync("test");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<org.npci.rustyclient.redis.HScanResult> future = client.hScanAsync("test", "0");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<Long> future = client.sAddAsync("test", "member1", "member2");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<java.util.Set<String>> future = client.sMembersAsync("test");
                assertThat(future).isNotNull();
            });

            assertDoesNotThrow(() -> {
                CompletableFuture<Long> future = client.delMultipleAsync("key1", "key2", "key3");
                assertThat(future).isNotNull();
            });
        }
    }

    // Integration tests - these would require a running Redis server
    // Commented out to avoid test failures in CI/CD without Redis

    /*
    @Test
    @DisplayName("Should perform basic string operations")
    void shouldPerformBasicStringOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Test set and get
            boolean setResult = client.set("test:key1", "value1");
            assertThat(setResult).isTrue();

            String getValue = client.get("test:key1");
            assertThat(getValue).isEqualTo("value1");

            // Test exists
            boolean exists = client.exists("test:key1");
            assertThat(exists).isTrue();

            // Test delete
            boolean deleteResult = client.delete("test:key1");
            assertThat(deleteResult).isTrue();

            // Verify deletion
            String deletedValue = client.get("test:key1");
            assertThat(deletedValue).isNull();
        }
    }

    @Test
    @DisplayName("Should perform hash operations")
    void shouldPerformHashOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Test hSet and hGet
            boolean hSetResult = client.hSet("test:hash1", "field1", "value1");
            assertThat(hSetResult).isTrue();

            String hGetValue = client.hGet("test:hash1", "field1");
            assertThat(hGetValue).isEqualTo("value1");

            // Test hMSet
            Map<String, String> fields = new HashMap<>();
            fields.put("field2", "value2");
            fields.put("field3", "value3");
            
            boolean hMSetResult = client.hMSet("test:hash1", fields);
            assertThat(hMSetResult).isTrue();

            // Test hGetAll
            Map<String, String> allFields = client.hGetAll("test:hash1");
            assertThat(allFields).hasSize(3);
            assertThat(allFields.get("field1")).isEqualTo("value1");
            assertThat(allFields.get("field2")).isEqualTo("value2");
            assertThat(allFields.get("field3")).isEqualTo("value3");

            // Test hExists
            boolean fieldExists = client.hExists("test:hash1", "field1");
            assertThat(fieldExists).isTrue();

            // Cleanup
            client.delete("test:hash1");
        }
    }

    @Test
    @DisplayName("Should perform async operations")
    void shouldPerformAsyncOperations() throws Exception {
        try (RedisClient client = new RedisClient(config)) {
            // Test async set
            CompletableFuture<Boolean> setFuture = client.setAsync("test:async1", "asyncvalue1");
            Boolean setResult = setFuture.get();
            assertThat(setResult).isTrue();

            // Test async get
            CompletableFuture<String> getFuture = client.getAsync("test:async1");
            String getValue = getFuture.get();
            assertThat(getValue).isEqualTo("asyncvalue1");

            // Test async delete
            CompletableFuture<Boolean> deleteFuture = client.deleteAsync("test:async1");
            Boolean deleteResult = deleteFuture.get();
            assertThat(deleteResult).isTrue();
        }
    }

    @Test
    @DisplayName("Should handle ping operation")
    void shouldHandlePingOperation() {
        try (RedisClient client = new RedisClient(config)) {
            boolean pingResult = client.ping();
            assertThat(pingResult).isTrue();
        }
    }

    @Test
    @DisplayName("Should perform new hash operations")
    void shouldPerformNewHashOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Setup test data
            client.hSet("test:newhash", "field1", "value1");
            client.hSet("test:newhash", "field2", "value2");
            client.hSet("test:newhash", "field3", "value3");
            client.hSet("test:newhash", "name", "John");
            client.hSet("test:newhash", "email", "<EMAIL>");

            // Test hLen
            long length = client.hLen("test:newhash");
            assertThat(length).isEqualTo(5);

            // Test hDel with multiple fields
            long deletedCount = client.hDel("test:newhash", "field2", "field3");
            assertThat(deletedCount).isEqualTo(2);

            // Verify deletion
            long newLength = client.hLen("test:newhash");
            assertThat(newLength).isEqualTo(3);

            // Test hScan
            org.npci.rustyclient.redis.HScanResult result = client.hScan("test:newhash", "0", "*name*", 10);
            assertThat(result).isNotNull();
            assertThat(result.getFields()).containsKey("name");

            // Test hScan pagination
            String cursor = "0";
            int totalFields = 0;
            do {
                org.npci.rustyclient.redis.HScanResult scanResult = client.hScan("test:newhash", cursor, null, 1);
                cursor = scanResult.getNextCursor();
                totalFields += scanResult.getFields().size();
            } while (!scanResult.isComplete());
            assertThat(totalFields).isEqualTo(3);

            // Cleanup
            client.delete("test:newhash");
        }
    }

    @Test
    @DisplayName("Should perform set operations")
    void shouldPerformSetOperations() {
        try (RedisClient client = new RedisClient(config)) {
            // Test sAdd
            long addedCount1 = client.sAdd("test:set", "member1", "member2", "member3");
            assertThat(addedCount1).isEqualTo(3);

            // Test sAdd with duplicates
            long addedCount2 = client.sAdd("test:set", "member2", "member4"); // member2 already exists
            assertThat(addedCount2).isEqualTo(1); // Only member4 is new

            // Test sMembers
            java.util.Set<String> members = client.sMembers("test:set");
            assertThat(members).hasSize(4);
            assertThat(members).contains("member1", "member2", "member3", "member4");

            // Cleanup
            client.delete("test:set");
        }
    }

    @Test
    @DisplayName("Should perform multiple key deletion")
    void shouldPerformMultipleKeyDeletion() {
        try (RedisClient client = new RedisClient(config)) {
            // Setup test data
            client.set("test:multi1", "value1");
            client.set("test:multi2", "value2");
            client.set("test:multi3", "value3");

            // Verify keys exist
            assertThat(client.exists("test:multi1")).isTrue();
            assertThat(client.exists("test:multi2")).isTrue();
            assertThat(client.exists("test:multi3")).isTrue();

            // Test delMultiple
            long deletedCount = client.delMultiple("test:multi1", "test:multi3", "test:nonexistent");
            assertThat(deletedCount).isEqualTo(2); // Only 2 keys existed

            // Verify deletion
            assertThat(client.exists("test:multi1")).isFalse();
            assertThat(client.exists("test:multi2")).isTrue(); // Not deleted
            assertThat(client.exists("test:multi3")).isFalse();

            // Cleanup
            client.delete("test:multi2");
        }
    }

    @Test
    @DisplayName("Should perform new async operations")
    void shouldPerformNewAsyncOperations() throws Exception {
        try (RedisClient client = new RedisClient(config)) {
            // Setup test data
            client.hSet("test:asynchash", "field1", "value1");
            client.hSet("test:asynchash", "field2", "value2");

            // Test async hash operations
            CompletableFuture<Long> hLenFuture = client.hLenAsync("test:asynchash");
            Long length = hLenFuture.get();
            assertThat(length).isEqualTo(2);

            CompletableFuture<Long> hDelFuture = client.hDelAsync("test:asynchash", "field1");
            Long deletedCount = hDelFuture.get();
            assertThat(deletedCount).isEqualTo(1);

            CompletableFuture<org.npci.rustyclient.redis.HScanResult> hScanFuture = client.hScanAsync("test:asynchash", "0");
            org.npci.rustyclient.redis.HScanResult scanResult = hScanFuture.get();
            assertThat(scanResult.getFields()).hasSize(1);

            // Test async set operations
            CompletableFuture<Long> sAddFuture = client.sAddAsync("test:asyncset", "member1", "member2");
            Long addedCount = sAddFuture.get();
            assertThat(addedCount).isEqualTo(2);

            CompletableFuture<java.util.Set<String>> sMembersFuture = client.sMembersAsync("test:asyncset");
            java.util.Set<String> members = sMembersFuture.get();
            assertThat(members).hasSize(2);

            // Test async key operations
            client.set("test:asynckey1", "value1");
            client.set("test:asynckey2", "value2");

            CompletableFuture<Long> delMultipleFuture = client.delMultipleAsync("test:asynckey1", "test:asynckey2");
            Long deletedKeys = delMultipleFuture.get();
            assertThat(deletedKeys).isEqualTo(2);

            // Cleanup
            client.delete("test:asynchash");
            client.delete("test:asyncset");
        }
    }
    */
}
