package org.npci.rustyclient.client.connection;

import io.grpc.ManagedChannel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.NodeRole;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;

/**
 * Tests for GrpcChannelFactory with focus on Linux compatibility.
 */
class GrpcChannelFactoryTest {

    private GrpcChannelFactory factory;
    private RustyClusterClientConfig config;

    @BeforeEach
    void setUp() {
        config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .preferIpv4(true)
                .build();
        factory = new GrpcChannelFactory(config);
    }

    @AfterEach
    void tearDown() {
        if (factory != null) {
            factory.close();
        }
    }

    @Test
    @DisplayName("Should create channel without throwing exceptions")
    void shouldCreateChannelWithoutThrowingExceptions() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);

        // When/Then - should not throw exceptions
        assertThatNoException().isThrownBy(() -> {
            ManagedChannel channel = factory.createChannel(nodeConfig);
            assertThat(channel).isNotNull();
            assertThat(channel.isShutdown()).isFalse();
        });
    }

    @Test
    @DisplayName("Should create channel with IP address")
    void shouldCreateChannelWithIpAddress() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("127.0.0.1", 50051, NodeRole.PRIMARY);

        // When/Then - should not throw exceptions
        assertThatNoException().isThrownBy(() -> {
            ManagedChannel channel = factory.createChannel(nodeConfig);
            assertThat(channel).isNotNull();
            assertThat(channel.isShutdown()).isFalse();
        });
    }

    @Test
    @DisplayName("Should cache channels for same address")
    void shouldCacheChannelsForSameAddress() {
        // Given
        NodeConfig nodeConfig1 = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig nodeConfig2 = new NodeConfig("localhost", 50051, NodeRole.SECONDARY);

        // When
        ManagedChannel channel1 = factory.createChannel(nodeConfig1);
        ManagedChannel channel2 = factory.createChannel(nodeConfig2);

        // Then - should return the same cached channel
        assertThat(channel1).isSameAs(channel2);
    }

    @Test
    @DisplayName("Should create different channels for different addresses")
    void shouldCreateDifferentChannelsForDifferentAddresses() {
        // Given
        NodeConfig nodeConfig1 = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        NodeConfig nodeConfig2 = new NodeConfig("localhost", 50052, NodeRole.SECONDARY);

        // When
        ManagedChannel channel1 = factory.createChannel(nodeConfig1);
        ManagedChannel channel2 = factory.createChannel(nodeConfig2);

        // Then - should return different channels
        assertThat(channel1).isNotSameAs(channel2);
    }

    @Test
    @DisplayName("Should handle IPv4 preference configuration")
    void shouldHandleIpv4PreferenceConfiguration() {
        // Given - config with IPv4 preference disabled
        RustyClusterClientConfig configNoIpv4Pref = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .preferIpv4(false)
                .build();
        
        GrpcChannelFactory factoryNoIpv4Pref = new GrpcChannelFactory(configNoIpv4Pref);
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);

        try {
            // When/Then - should still work without throwing exceptions
            assertThatNoException().isThrownBy(() -> {
                ManagedChannel channel = factoryNoIpv4Pref.createChannel(nodeConfig);
                assertThat(channel).isNotNull();
            });
        } finally {
            factoryNoIpv4Pref.close();
        }
    }

    @Test
    @DisplayName("Should handle secure connection configuration")
    void shouldHandleSecureConnectionConfiguration() {
        // Given - we can't easily test actual TLS without certificates
        // but we can test that the factory handles the configuration
        assertThat(config.isUseSecureConnection()).isFalse();
        
        // This test mainly ensures the factory can be created with secure config
        // without throwing exceptions during construction
        RustyClusterClientConfig secureConfig = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .preferIpv4(true)
                // Note: We don't set useSecureConnection here because it would require
                // a valid certificate path, which we don't have in tests
                .build();
        
        GrpcChannelFactory secureFactory = new GrpcChannelFactory(secureConfig);
        
        try {
            assertThat(secureFactory).isNotNull();
        } finally {
            secureFactory.close();
        }
    }

    @Test
    @DisplayName("Should close gracefully")
    void shouldCloseGracefully() {
        // Given
        NodeConfig nodeConfig = new NodeConfig("localhost", 50051, NodeRole.PRIMARY);
        ManagedChannel channel = factory.createChannel(nodeConfig);
        
        assertThat(channel.isShutdown()).isFalse();

        // When
        factory.close();

        // Then - should close without throwing exceptions
        // Note: The actual shutdown might be asynchronous, so we just verify
        // that the close method doesn't throw
        assertThatNoException().isThrownBy(() -> factory.close());
    }

    @Test
    @DisplayName("Should handle invalid hostnames gracefully")
    void shouldHandleInvalidHostnamesGracefully() {
        // Given - a hostname that doesn't exist
        NodeConfig nodeConfig = new NodeConfig("non-existent-host-12345", 50051, NodeRole.PRIMARY);

        // When/Then - should create channel without throwing during creation
        // (actual connection errors would occur when trying to use the channel)
        assertThatNoException().isThrownBy(() -> {
            ManagedChannel channel = factory.createChannel(nodeConfig);
            assertThat(channel).isNotNull();
        });
    }
}
