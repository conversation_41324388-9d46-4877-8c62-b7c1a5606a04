# RustyCluster Java Client Library

A comprehensive Java client library providing dual connectivity options for distributed key-value operations. Built with Java 17 and modern language features.

## Overview

This library provides **two separate, production-ready clients**:

1. **RustyClusterClient** - For connecting to RustyCluster (Rust-based distributed key-value store) via gRPC
2. **RedisClient** - For direct Redis connectivity with full Redis protocol support

### Architecture Decision: Separate Clients

Instead of a unified client, we implemented separate clients because:

✅ **Minimal Dependencies** - Services using only one client don't pull unnecessary dependencies
✅ **Clear Separation of Concerns** - Each client has focused responsibility
✅ **Independent Evolution** - Clients can evolve independently
✅ **Better Resource Management** - No unused connection pools
✅ **Microservices Best Practices** - Each service only includes what it needs

## Features

### RustyClusterClient Features
- **High Availability**: Connect to multiple RustyCluster nodes with primary, secondary, and tertiary prioritization
- **Optimized Connection Pooling**: Efficient gRPC connection management with channel reuse and configurable pool sizes
- **Automatic Failover & Failback**: Seamlessly switch to backup nodes and automatically return to primary when available
- **High-Throughput Performance**: Optimized for high-throughput scenarios with performance monitoring
- **Comprehensive API**: Support for all RustyCluster operations including string, numeric, hash, set, and batch operations
- **Configurable Retries**: Automatic retry mechanism with configurable delays and limits
- **TLS Support**: Optional secure connections with TLS
- **Authentication**: Username/password authentication with session token management and automatic re-authentication

### RedisClient Features
- **Direct Redis Protocol**: Native Redis connectivity with full protocol support
- **Dual Operation Modes**: Both synchronous and asynchronous operations
- **Automatic Failover**: Seamless failover between primary, secondary, and tertiary Redis nodes
- **Connection Pooling**: High-performance connection pooling with Apache Commons Pool2
- **Authentication Support**: Redis ACL and legacy password authentication
- **SSL/TLS Support**: Secure connections with custom certificate support
- **Cluster Mode**: Redis Cluster support with automatic redirections
- **Performance Presets**: Optimized configurations for different use cases
- **Comprehensive Monitoring**: JMX metrics and connection pool statistics

### Shared Features
- **Unified API Design**: Both clients follow similar patterns and method signatures
- **Production Ready**: Extensive error handling, logging, and resource management
- **Java 17 Features**: Modern language features including records, pattern matching, and enhanced switch expressions

## Client Selection Guide

### When to Use RustyClusterClient
- **Distributed Systems**: When you need RustyCluster's distributed key-value store capabilities
- **High Consistency**: For operations requiring strong consistency guarantees
- **Custom Operations**: When using RustyCluster-specific features like batch operations
- **gRPC Integration**: When your system already uses gRPC infrastructure

### When to Use RedisClient
- **Redis Compatibility**: When you need standard Redis operations and data structures
- **Caching Layer**: For high-performance caching scenarios
- **Redis Ecosystem**: When integrating with existing Redis tools and monitoring
- **Direct Protocol**: When you need direct Redis protocol communication

### Dual Client Usage
Some microservices may benefit from using both clients:
- **RustyCluster** for persistent, distributed data
- **Redis** for temporary caching and session storage

## Requirements

- Java 17 or higher
- Maven 3.6 or higher (for building from source)

## Installation

### Maven

Add the following dependency to your `pom.xml`:

```xml
<dependency>
    <groupId>com.rustycluster</groupId>
    <artifactId>rustycluster-java-client</artifactId>
    <version>1.0.0</version>
</dependency>
```

### Gradle

Add the following dependency to your `build.gradle`:

```groovy
implementation 'com.rustycluster:rustycluster-java-client:1.0.0'
```

## Quick Start

### RustyClusterClient Quick Start

```java
import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.config.NodeRole;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

public class RustyClusterQuickStart {
    public static void main(String[] args) {
        // Create client configuration
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNode("localhost", 50051, NodeRole.PRIMARY)
                .addNode("localhost", 50052, NodeRole.SECONDARY)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .authentication("username", "password")  // Optional authentication
                .build();

        // Single client supports both sync and async operations
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Synchronous operations
            client.set("greeting", "Hello, RustyCluster!");
            String value = client.get("greeting");
            System.out.println(value);  // Output: Hello, RustyCluster!

            // Hash operations
            client.hSet("user:1001", "name", "John Doe");
            String name = client.hGet("user:1001", "name");

            // Set operations
            client.sAdd("user:tags", "java", "microservices");
            List<String> tags = client.sMembers("user:tags");

            // Asynchronous operations
            CompletableFuture<Boolean> setFuture = client.setAsync("async-key", "async-value");
            CompletableFuture<String> getFuture = client.getAsync("async-key");

            // Chain async operations
            setFuture.thenCompose(result -> getFuture)
                     .thenAccept(retrievedValue ->
                         System.out.println("Async value: " + retrievedValue))
                     .join();
        }
    }
}
```

### RedisClient Quick Start

```java
import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.config.RedisClientConfig;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

public class RedisQuickStart {
    public static void main(String[] args) {
        // Create Redis client configuration
        RedisClientConfig config = RedisClientConfig.builder()
            .addPrimaryNode("localhost", 6379)
            .authentication("redis-user", "redis-password")  // Optional
            .database(1)  // Optional database selection
            .build();

        // Use the Redis client
        try (RedisClient client = new RedisClient(config)) {
            // String operations
            client.set("key1", "value1");
            String value = client.get("key1");
            System.out.println(value);  // Output: value1

            // Hash operations
            client.hSet("user:1001", "name", "John Doe");
            String name = client.hGet("user:1001", "name");

            // Set operations
            client.sAdd("user:tags", "redis", "caching");
            Set<String> members = client.sMembers("user:tags");

            // Async operations
            CompletableFuture<Boolean> setFuture = client.setAsync("key2", "value2");
            CompletableFuture<String> getFuture = client.getAsync("key2");

            // Chain operations
            setFuture.thenCompose(result -> getFuture)
                     .thenAccept(retrievedValue ->
                         System.out.println("Async value: " + retrievedValue))
                     .join();
        }
    }
}
```

### Dual Client Usage

```java
import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.redis.RedisClient;
import org.npci.rustyclient.redis.config.RedisClientConfig;

public class DualClientExample {
    public static void main(String[] args) {
        // Configure both clients
        RustyClusterClientConfig rustyConfig = RustyClusterClientConfig.builder()
            .addPrimaryNode("rusty-primary", 50051)
            .authentication("rusty-user", "rusty-pass")
            .build();

        RedisClientConfig redisConfig = RedisClientConfig.builder()
            .addPrimaryNode("redis-server", 6379)
            .authentication("redis-user", "redis-pass")
            .database(1)
            .build();

        // Use both clients
        try (RustyClusterClient rustyClient = new RustyClusterClient(rustyConfig);
             RedisClient redisClient = new RedisClient(redisConfig)) {

            // Route operations based on use case
            rustyClient.set("persistent-data", "value");  // Persistent storage
            redisClient.setEx("cache-data", "value", 300); // Temporary cache

            // Different operations for different needs
            rustyClient.batchWrite(operations);  // Batch operations
            redisClient.sAdd("session:active", userId);  // Session management
        }
    }
}
```

## Unified Client Architecture

The `RustyClusterClient` provides **both synchronous and asynchronous operations** in a single client, eliminating the need for separate client instances:

### **Synchronous vs Asynchronous Methods**

| Operation Type | Synchronous Method | Asynchronous Method | Return Type |
|----------------|-------------------|-------------------|-------------|
| **Set** | `set(key, value)` | `setAsync(key, value)` | `boolean` / `CompletableFuture<Boolean>` |
| **Get** | `get(key)` | `getAsync(key)` | `String` / `CompletableFuture<String>` |
| **Delete** | `delete(key)` | `deleteAsync(key)` | `boolean` / `CompletableFuture<Boolean>` |
| **Hash Set** | `hSet(key, field, value)` | `hSetAsync(key, field, value)` | `boolean` / `CompletableFuture<Boolean>` |
| **Hash Get** | `hGet(key, field)` | `hGetAsync(key, field)` | `String` / `CompletableFuture<String>` |
| **Batch Write** | `batchWrite(operations)` | `batchWriteAsync(operations)` | `List<Boolean>` / `CompletableFuture<List<Boolean>>` |

### **Benefits of Unified Client**

✅ **Single Client Instance**: No need to manage separate sync/async clients
✅ **Consistent API**: Same method names with `Async` suffix for async versions
✅ **Shared Configuration**: Both sync and async operations use the same connection pools and settings
✅ **Resource Efficiency**: Single set of connection managers and authentication state
✅ **Simplified Usage**: Choose sync or async per operation, not per client instance

### **Usage Examples**

```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    // Mix sync and async operations as needed

    // Synchronous for simple operations
    boolean authResult = client.authenticate();

    // Asynchronous for high-throughput operations
    List<CompletableFuture<Boolean>> futures = new ArrayList<>();
    for (int i = 0; i < 1000; i++) {
        futures.add(client.setAsync("key" + i, "value" + i));
    }

    // Wait for all async operations to complete
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

    // Synchronous for final verification
    String result = client.get("key999");
}
```

## Configuration

The client can be configured with various options and supports flexible node configuration:

### Basic Configuration

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        // Add nodes with priorities
        .addPrimaryNode("primary.example.com", 50051)
        .addSecondaryNode("secondary1.example.com", 50051)
        .addTertiaryNode("tertiary.example.com", 50051)

        // Connection pool settings
        .maxConnectionsPerNode(10)

        // Timeouts
        .connectionTimeout(5, TimeUnit.SECONDS)
        .readTimeout(3, TimeUnit.SECONDS)
        .writeTimeout(3, TimeUnit.SECONDS)

        // Retry settings
        .maxRetries(3)
        .retryDelay(500, TimeUnit.MILLISECONDS)

        // Optional TLS configuration
        .useSecureConnection("/path/to/certificate.pem")

        // Optional authentication
        .authentication("username", "password")

        .build();
```

### Simplified Node Configuration

The client supports automatic role assignment based on the order nodes are added:

```java
// Single node (automatically PRIMARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051")
        .build();

// Two nodes (first is PRIMARY, second is SECONDARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052")
        .build();

// Three or more nodes (first is PRIMARY, second is SECONDARY, rest are TERTIARY)
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052", "localhost:50053", "localhost:50054")
        .build();
```

### Advanced Node Configuration

For more control, you can explicitly specify node roles:

```java
// Add multiple nodes of the same role using string format "host:port"
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addPrimaryNodes("primary1.example.com:50051", "primary2.example.com:50051")
        .addSecondaryNodes("secondary1.example.com:50051", "secondary2.example.com:50051")
        .addTertiaryNodes("tertiary1.example.com:50051", "tertiary2.example.com:50051")
        .build();

// Add nodes with custom roles
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNode("node1.example.com", 50051, NodeRole.PRIMARY)
        .addNode("node2.example.com", 50051, NodeRole.SECONDARY)
        .addNodes(NodeRole.TERTIARY, "node3.example.com:50051", "node4.example.com:50051")
        .build();

// Mix automatic and explicit role assignment
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052") // PRIMARY and SECONDARY
        .addTertiaryNodes("localhost:50053", "localhost:50054") // Additional TERTIARY nodes
        .build();
```

## Authentication

The RustyCluster Java client supports username/password authentication with session token management. When authentication is configured, the client will automatically authenticate with the server and include session tokens in subsequent requests.

### Configuring Authentication

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
        .addNodes("localhost:50051", "localhost:50052")
        .authentication("username", "password")
        .build();
```

### Using Authentication

```java
try (RustyClusterClient client = new RustyClusterClient(config)) {
    // Authenticate with the server (optional - done automatically on first operation)
    boolean authResult = client.authenticate();
    if (authResult) {
        System.out.println("Authentication successful");

        // Perform operations - session token is automatically included
        client.set("key", "value");
        String value = client.get("key");
    } else {
        System.err.println("Authentication failed");
    }
}
```

### Authentication Features

- **Automatic Authentication**: The client automatically authenticates on the first operation if credentials are configured
- **Session Token Management**: Session tokens are automatically managed and included in all requests
- **Secure Token Storage**: Session tokens are stored securely in memory using atomic references
- **Authentication State Tracking**: The client tracks authentication state and can re-authenticate if needed
- **Interceptor Integration**: Authentication headers are automatically added to gRPC calls via interceptors

### Authentication Methods

```java
// Check if authentication is configured
boolean hasAuth = config.hasAuthentication();

// Manually authenticate (usually not needed)
boolean success = client.authenticate();

// Check authentication state
boolean isAuthenticated = authenticationManager.isAuthenticated();

// Get current session token (for debugging)
String token = authenticationManager.getSessionToken();

// Clear authentication state
authenticationManager.clearAuthentication();
```

## API Reference

Both clients provide similar APIs with consistent method signatures. Here's a comprehensive reference:

### String Operations

#### RustyClusterClient

```java
// Set a key-value pair
boolean set(String key, String value);
boolean set(String key, String value, boolean skipReplication);
CompletableFuture<Boolean> setAsync(String key, String value);
CompletableFuture<Boolean> setAsync(String key, String value, boolean skipReplication);

// Get a value
String get(String key);
CompletableFuture<String> getAsync(String key);

// Delete a key
boolean delete(String key);
boolean delete(String key, boolean skipReplication);
CompletableFuture<Boolean> deleteAsync(String key);
CompletableFuture<Boolean> deleteAsync(String key, boolean skipReplication);

// Set with expiration
boolean setEx(String key, String value, long ttl);
boolean setEx(String key, String value, long ttl, boolean skipReplication);

// Set expiration on existing key
boolean setExpiry(String key, long ttl);
boolean setExpiry(String key, long ttl, boolean skipReplication);
```

### Numeric Operations

```java
// Increment a value
long incrBy(String key, long value);
long incrBy(String key, long value, boolean skipReplication);

// Decrement a value
long decrBy(String key, long value);
long decrBy(String key, long value, boolean skipReplication);

// Increment a float value
double incrByFloat(String key, double value);
double incrByFloat(String key, double value, boolean skipReplication);
```

### Hash Operations

```java
// Set a hash field
boolean hSet(String key, String field, String value);
boolean hSet(String key, String field, String value, boolean skipReplication);
CompletableFuture<Boolean> hSetAsync(String key, String field, String value);
CompletableFuture<Boolean> hSetAsync(String key, String field, String value, boolean skipReplication);

// Set multiple hash fields at once (NEW)
Map<String, String> fields = Map.of("name", "John", "email", "<EMAIL>");
boolean hMSet(String key, Map<String, String> fields);
boolean hMSet(String key, Map<String, String> fields, boolean skipReplication);
CompletableFuture<Boolean> hMSetAsync(String key, Map<String, String> fields);
CompletableFuture<Boolean> hMSetAsync(String key, Map<String, String> fields, boolean skipReplication);

// Check if a hash field exists (NEW)
boolean hExists(String key, String field);
CompletableFuture<Boolean> hExistsAsync(String key, String field);

// Get a hash field
String hGet(String key, String field);
CompletableFuture<String> hGetAsync(String key, String field);

// Get all hash fields
Map<String, String> hGetAll(String key);
CompletableFuture<Map<String, String>> hGetAllAsync(String key);

// Increment a hash field
long hIncrBy(String key, String field, long value);
long hIncrBy(String key, String field, long value, boolean skipReplication);

// Decrement a hash field
long hDecrBy(String key, String field, long value);
long hDecrBy(String key, String field, long value, boolean skipReplication);

// Increment a float hash field
double hIncrByFloat(String key, String field, double value);
double hIncrByFloat(String key, String field, double value, boolean skipReplication);

// Delete multiple hash fields (NEW)
int hDel(String key, String... fields);
CompletableFuture<Integer> hDelAsync(String key, String... fields);

// Scan hash fields with pagination (NEW)
HScanResult hScan(String key, long cursor, String pattern, Integer count);
CompletableFuture<HScanResult> hScanAsync(String key, long cursor, String pattern, Integer count);

// Get hash length (NEW)
int hLen(String key);
CompletableFuture<Integer> hLenAsync(String key);
```

### Set Operations (NEW)

#### RustyClusterClient
```java
// Add members to a set
int sAdd(String key, String... members);
int sAdd(String key, String[] members, boolean skipReplication);
CompletableFuture<Integer> sAddAsync(String key, String... members);
CompletableFuture<Integer> sAddAsync(String key, String[] members, boolean skipReplication);

// Get all members of a set
List<String> sMembers(String key);
CompletableFuture<List<String>> sMembersAsync(String key);
```

### Key Operations

#### RustyClusterClient
```java
// Delete multiple keys (NEW)
int delMultiple(String... keys);
int delMultiple(String[] keys, boolean skipReplication);
CompletableFuture<Integer> delMultipleAsync(String... keys);
CompletableFuture<Integer> delMultipleAsyncAsync(String[] keys, boolean skipReplication);
```

## RedisClient API Reference

### String Operations

```java
// Basic operations
boolean set(String key, String value);
String get(String key);
boolean exists(String key);
boolean delete(String key);

// With expiration
boolean setEx(String key, String value, long ttlSeconds);
boolean expire(String key, long ttlSeconds);

// Conditional set
boolean setNX(String key, String value); // Only if not exists

// Numeric operations
long incr(String key);
long incrBy(String key, long value);
long decrBy(String key, long value);
double incrByFloat(String key, double value);

// Async versions
CompletableFuture<Boolean> setAsync(String key, String value);
CompletableFuture<String> getAsync(String key);
CompletableFuture<Boolean> existsAsync(String key);
CompletableFuture<Boolean> deleteAsync(String key);
```

### Hash Operations

```java
// Single field operations
boolean hSet(String key, String field, String value);
String hGet(String key, String field);
boolean hExists(String key, String field);

// Multiple fields
boolean hMSet(String key, Map<String, String> fields);
Map<String, String> hGetAll(String key);

// Delete multiple fields
long hDel(String key, String... fields);

// Get hash length
long hLen(String key);

// Scan hash fields with pagination
HScanResult hScan(String key, String cursor, String pattern, Integer count);

// Numeric operations
long hIncrBy(String key, String field, long value);
double hIncrByFloat(String key, String field, double value);

// Async versions
CompletableFuture<Boolean> hSetAsync(String key, String field, String value);
CompletableFuture<String> hGetAsync(String key, String field);
CompletableFuture<Long> hDelAsync(String key, String... fields);
CompletableFuture<Long> hLenAsync(String key);
CompletableFuture<HScanResult> hScanAsync(String key, String cursor, String pattern, Integer count);
```

### Set Operations

```java
// Add members to a set
long sAdd(String key, String... members);

// Get all members of a set
Set<String> sMembers(String key);

// Async versions
CompletableFuture<Long> sAddAsync(String key, String... members);
CompletableFuture<Set<String>> sMembersAsync(String key);
```

### Key Operations

```java
// Delete multiple keys
long delMultiple(String... keys);

// Async version
CompletableFuture<Long> delMultipleAsync(String... keys);
```

### Script Operations

```java
// Load and execute Lua scripts
String scriptLoad(String script);
String evalSha(String sha, List<String> keys, List<String> args);

// Async versions
CompletableFuture<String> scriptLoadAsync(String script);
CompletableFuture<String> evalShaAsync(String sha, List<String> keys, List<String> args);
```

### Key Existence and Conditional Operations

```java
// Check if a key exists (NEW)
boolean exists(String key);
CompletableFuture<Boolean> existsAsync(String key);

// Set key only if it doesn't exist (NEW)
boolean setNX(String key, String value);
boolean setNX(String key, String value, boolean skipReplication);
CompletableFuture<Boolean> setNXAsync(String key, String value);
CompletableFuture<Boolean> setNXAsync(String key, String value, boolean skipReplication);
```

### Lua Scripting Operations

```java
// Load a Lua script and get its SHA hash (NEW)
String sha = client.loadScript("return redis.call('GET', KEYS[1])");
CompletableFuture<String> shaAsync = client.loadScriptAsync("return redis.call('GET', KEYS[1])");

// Execute a loaded script by SHA (NEW)
String result = client.evalSha(sha, List.of("key1"), List.of("arg1"));
String result = client.evalSha(sha, List.of("key1"), List.of("arg1"), true);
CompletableFuture<String> resultAsync = client.evalShaAsync(sha, List.of("key1"), List.of("arg1"));
CompletableFuture<String> resultAsync = client.evalShaAsync(sha, List.of("key1"), List.of("arg1"), true);
```

### Health and Connectivity

```java
// Perform health check (NEW)
boolean isHealthy = client.healthCheck();
CompletableFuture<Boolean> isHealthyAsync = client.healthCheckAsync();

// Ping cluster for connectivity check (NEW)
boolean pingResult = client.ping();
CompletableFuture<Boolean> pingResultAsync = client.pingAsync();

// Authentication
boolean authResult = client.authenticate();
CompletableFuture<Boolean> authResultAsync = client.authenticateAsync();
```

### Batch Operations

```java
// Create batch operations
BatchOperationBuilder batchBuilder = new BatchOperationBuilder()
        .addSet("key1", "value1")
        .addSet("key2", "value2")
        .addSetEx("key3", "value3", 300)
        .addHSet("hash1", "field1", "value1")
        .addHMSet("hash2", Map.of("field1", "value1", "field2", "value2"))  // NEW
        .addSetNX("lock:key", "locked");  // NEW

// Execute batch - both sync and async
List<BatchOperation> operations = batchBuilder.build();
List<Boolean> results = client.batchWrite(operations);
CompletableFuture<List<Boolean>> resultsAsync = client.batchWriteAsync(operations);
```

## Error Handling

The client throws `NoAvailableNodesException` when all nodes are unavailable after the configured number of retries.

```java
try {
    client.set("key", "value");
} catch (NoAvailableNodesException e) {
    System.err.println("All RustyCluster nodes are unavailable: " + e.getMessage());
}
```

## Java 17 Features

This client library takes advantage of modern Java 17 features:

- **Records**: Immutable data classes with built-in equals, hashCode, and toString methods
- **Pattern Matching**: Simplified instanceof checks and type casting
- **Enhanced Switch Expressions**: More concise and powerful switch statements
- **Text Blocks**: Multi-line string literals for improved readability
- **Sealed Classes**: Restricting which classes can extend or implement a class or interface
- **Stream API Enhancements**: New methods like toList() for more concise code

## Testing

The client library includes comprehensive test coverage:

### Running Tests

```bash
# Run all tests
mvn test

# Run tests with coverage report
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=RustyClusterClientConfigTest

# Run tests in a specific package
mvn test -Dtest="com.rustycluster.client.config.*"
```

### Test Categories

1. **Configuration Tests** (`RustyClusterClientConfigTest`)
   - Tests for node configuration with automatic role assignment
   - Tests for explicit role assignment
   - Tests for validation and error handling

2. **Node Configuration Tests** (`NodeConfigTest`, `NodeRoleTest`)
   - Tests for NodeConfig record functionality
   - Tests for NodeRole priority ordering

3. **Connection Management Tests** (`ConnectionManagerTest`)
   - Tests for failover behavior
   - Tests for retry mechanisms
   - Tests for connection pool management

4. **Client API Tests** (`RustyClusterClientTest`)
   - Tests for all client operations (set, get, delete, etc.)
   - Tests for batch operations
   - Tests for error handling

5. **Batch Operation Tests** (`BatchOperationBuilderTest`)
   - Tests for building batch operations
   - Tests for all operation types
   - Tests for method chaining

6. **Authentication Tests** (`AuthenticationManagerTest`)
   - Tests for authentication with valid credentials
   - Tests for authentication failure scenarios
   - Tests for session token management
   - Tests for authentication state tracking

### Test Coverage

The test suite covers:
- ✅ Configuration validation and node assignment
- ✅ Connection management and failover
- ✅ All client API methods
- ✅ Batch operation building
- ✅ Authentication and session management
- ✅ Error handling and edge cases
- ✅ Java 17 features (records, pattern matching, etc.)

### Mock Testing

Tests use Mockito for mocking gRPC stubs and connection pools, allowing for:
- Isolated unit testing
- Simulation of network failures
- Testing of retry and failover logic
- Verification of method calls and parameters

## Building from Source

1. Clone the repository
2. Generate gRPC classes and build with Maven:

```bash
# Generate gRPC classes from protobuf
mvn protobuf:compile protobuf:compile-custom

# Build the project
mvn clean package
```

3. Run tests:

```bash
mvn test
```

### Note on gRPC Code Generation

The project uses protobuf to generate gRPC client classes. The generated classes will be placed in:
- `target/generated-sources/protobuf/java/` - Protocol buffer message classes
- `target/generated-sources/protobuf/grpc-java/` - gRPC service stubs

If you encounter compilation issues related to `javax.annotation.Generated`, ensure that both `javax.annotation-api` and `jakarta.annotation-api` dependencies are included in your classpath (both are included in the pom.xml for compatibility).

## Performance Optimizations

The client library includes several performance optimizations for high-throughput scenarios:

### **Connection Management**
- **Channel Reuse**: gRPC channels are cached and reused across connections to reduce overhead
- **Connection Pooling**: Apache Commons Pool2 with optimized settings for high concurrency
- **Connection Warm-up**: Pre-creates minimum connections to reduce first-operation latency

### **High-Throughput Settings**
```java
// Optimized configuration for high throughput
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("node1:50051", "node2:50051", "node3:50051")
    .maxConnectionsPerNode(50)           // Increased connection pool
    .connectionTimeout(2, TimeUnit.SECONDS)  // Faster timeouts
    .readTimeout(1, TimeUnit.SECONDS)
    .writeTimeout(1, TimeUnit.SECONDS)
    .maxRetries(1)                       // Fewer retries for faster failure detection
    .retryDelay(50, TimeUnit.MILLISECONDS)   // Reduced retry delay
    .build();
```

### **Performance Presets**

The client provides three optimized configuration presets:

#### **High-Throughput Preset**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("localhost:50051", "localhost:50052")
    .highThroughputPreset() // 50 connections, 1s timeouts, 1 retry
    .build();
```

#### **Low-Latency Preset**
```java
config.lowLatencyPreset(); // 10 connections, 500ms timeouts, 0 retries
```

#### **Balanced Preset**
```java
config.balancedPreset(); // 20 connections, 2-3s timeouts, 2 retries
```

### **Performance Monitoring**
- **Real-time Metrics**: Operation counters, latency tracking, throughput calculation
- **Connection Pool Metrics**: Active/idle connections, pool utilization
- **Failover Tracking**: Node failure events and recovery times

```java
// Get performance statistics
PerformanceMetrics.PerformanceStats stats = client.getPerformanceStats();
logger.info("Throughput: {:.2f} ops/sec, Avg Latency: {:.2f}ms",
    stats.throughputOpsPerSec(), stats.avgLatencyMs());
```

## Automatic Failover & Failback

### **Failover Features**
- **Automatic Node Switching**: Seamlessly switches to backup nodes when primary fails
- **Priority-Based Selection**: PRIMARY → SECONDARY → TERTIARY priority order
- **Authentication Handling**: Automatically clears and re-establishes authentication during failover

### **Automatic Failback**
The client includes an automatic failback mechanism that periodically checks if higher-priority nodes have recovered:

- **Background Monitoring**: Periodically checks if higher-priority nodes are healthy
- **Configurable Intervals**: Default 30 seconds, customizable
- **Priority-Based Recovery**: Always tries to use the highest-priority available node
- **Non-Blocking Operation**: Runs in background threads without affecting performance

```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("primary-server", 50051)
    .addSecondaryNode("secondary-server", 50052)
    .enableFailback(true)  // Enable automatic failback (default: true)
    .failbackCheckInterval(15, TimeUnit.SECONDS)    // Check every 15 seconds
    .failbackHealthCheckRetries(3)                  // Require 3 successful health checks
    .build();
```

### **Authentication Failover**
When authentication is configured, the client automatically handles authentication state during failover:

1. **Authentication State Clearing**: Clears authentication when switching nodes
2. **Automatic Re-authentication**: Re-authenticates with new nodes automatically
3. **Session Continuity**: Maintains authentication across node switches

## Test Coverage

The client library includes comprehensive test coverage:

### **Test Categories**

1. **Configuration Tests** (`RustyClusterClientConfigTest`)
   - Tests for node configuration with automatic role assignment
   - Tests for explicit role assignment
   - Tests for validation and error handling

2. **Node Configuration Tests** (`NodeConfigTest`, `NodeRoleTest`)
   - Tests for NodeConfig record functionality
   - Tests for NodeRole priority ordering

3. **Connection Management Tests** (`ConnectionManagerTest`)
   - Tests for failover behavior
   - Tests for retry mechanisms
   - Tests for connection pool management

4. **Client API Tests** (`RustyClusterClientTest`)
   - Tests for all client operations (set, get, delete, etc.)
   - Tests for batch operations
   - Tests for error handling
   - Tests for new methods (HMSET, HEXISTS, EXISTS, SETNX, LoadScript, EvalSha, HealthCheck, Ping)

5. **Batch Operation Tests** (`BatchOperationBuilderTest`)
   - Tests for building batch operations
   - Tests for all operation types
   - Tests for method chaining

6. **Authentication Tests** (`AuthenticationManagerTest`)
   - Tests for authentication with valid credentials
   - Tests for authentication failure scenarios
   - Tests for session token management
   - Tests for authentication state tracking

### **Coverage Metrics**

The test suite covers:
- ✅ Configuration validation and node assignment
- ✅ Connection management and failover
- ✅ All client API methods (including new methods)
- ✅ Batch operation building
- ✅ Authentication and session management
- ✅ Error handling and edge cases
- ✅ Java 17 features (records, pattern matching, etc.)

### **Running Tests with Coverage**

```bash
# Run all tests
mvn test

# Run tests with coverage report
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=RustyClusterClientTest

# Run tests in a specific package
mvn test -Dtest="org.npci.rustyclient.client.config.*"
```

Coverage reports are available at `target/site/jacoco/index.html` after running tests with JaCoCo.

## Examples

The library includes comprehensive examples demonstrating both clients:

### RustyClusterClient Examples

#### `examples/RustyClusterClientNewMethodsExample.java`
- Hash operations: hDel (multiple fields), hScan, hLen
- Set operations: sAdd, sMembers
- Key operations: delMultiple
- Async versions of all new methods
- Pagination examples with hScan

### RedisClient Examples

#### `examples/RedisClientExample.java`
- Basic string, hash, and key operations
- Multi-node setup with authentication
- High-throughput scenarios
- Redis cluster mode configuration
- Asynchronous operations

#### `examples/RedisClientNewMethodsExample.java`
- Hash operations: hDel (multiple fields), hScan, hLen
- Set operations: sAdd, sMembers
- Key operations: delMultiple
- Async versions of all new methods
- Pagination examples with hScan

### Dual Client Examples

#### `examples/DualClientExample.java`
- Using both RustyCluster and Redis clients together
- Routing operations based on use case
- Separate connection pools and authentication
- Resource management best practices

## Configuration Comparison

| Feature | RustyClusterClient | RedisClient |
|---------|-------------------|-------------|
| **Connection** | gRPC to Rust cluster | Direct Redis protocol |
| **Authentication** | Custom auth manager | Redis ACL/password |
| **Pool Management** | gRPC connection pools | Jedis connection pools |
| **Failover** | Primary → Secondary → Tertiary | Primary → Secondary → Tertiary |
| **Async Support** | ✅ CompletableFuture | ✅ CompletableFuture |
| **SSL/TLS** | ✅ Custom certificates | ✅ Redis SSL |
| **Monitoring** | ✅ JMX metrics | ✅ JMX metrics |
| **Cluster Mode** | ✅ RustyCluster nodes | ✅ Redis Cluster |
| **Performance Presets** | ✅ High-throughput, Low-latency | ✅ High-throughput, Low-latency |

## Deployment Scenarios

### Scenario 1: RustyCluster Only
```xml
<!-- microservice-a/pom.xml -->
<dependencies>
    <dependency>
        <groupId>com.rustycluster</groupId>
        <artifactId>rustycluster-java-client</artifactId>
        <version>1.0.0</version>
        <!-- Only RustyCluster classes, no Redis dependencies -->
    </dependency>
</dependencies>
```

### Scenario 2: Redis Only
```xml
<!-- microservice-b/pom.xml -->
<dependencies>
    <dependency>
        <groupId>com.rustycluster</groupId>
        <artifactId>rustycluster-java-client</artifactId>
        <version>1.0.0</version>
        <!-- Only Redis classes, no gRPC dependencies -->
    </dependency>
</dependencies>
```

### Scenario 3: Both Clients
```xml
<!-- microservice-c/pom.xml -->
<dependencies>
    <dependency>
        <groupId>com.rustycluster</groupId>
        <artifactId>rustycluster-java-client</artifactId>
        <version>1.0.0</version>
        <!-- Both RustyCluster and Redis classes -->
    </dependency>
</dependencies>
```

## Troubleshooting

### **Common Issues and Solutions**

#### **DEADLINE_EXCEEDED Errors**
**Problem**: `DEADLINE_EXCEEDED: ClientCall started after CallOptions deadline was exceeded`

**Solution**: This has been fixed in the current version. The client now applies timeouts per-operation rather than per-stub, eliminating stale deadline issues.

#### **Authentication Failures After Failover**
**Problem**: Authentication fails when switching from primary to secondary node

**Solution**: The client automatically clears authentication state during failover and re-authenticates with the new node. Ensure your authentication credentials are valid for all nodes.

#### **Low Throughput**
**Solutions**:
- Increase `maxConnectionsPerNode`
- Use `highThroughputPreset()`
- Consider async operations for high-concurrency scenarios
- Check server capacity and network latency

#### **High Latency**
**Solutions**:
- Use `lowLatencyPreset()`
- Reduce connection pool size
- Check network latency between client and server
- Consider local deployment for critical applications

#### **Connection Issues**
**Solutions**:
- Monitor JMX metrics for connection pool status
- Check connection pool settings
- Verify network connectivity to all nodes
- Review server logs for connection errors

### **Performance Tuning**

#### **For Maximum Throughput**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("node1:50051", "node2:50051", "node3:50051")
    .highThroughputPreset()
    .maxConnectionsPerNode(50) // Even more connections if needed
    .build();
```

#### **For Low Latency**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("localhost", 50051)
    .lowLatencyPreset()
    .maxConnectionsPerNode(5) // Fewer connections for lower latency
    .build();
```

#### **For Production Environments**
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addNodes("primary:50051", "secondary:50051", "tertiary:50051")
    .balancedPreset() // Good balance of performance and reliability
    .useSecureConnection("/path/to/cert.pem") // TLS in production
    .authentication("username", "password") // Authentication
    .build();
```

## **Linux Compatibility**

### **Common Issue: UnsupportedAddressTypeException**

If you encounter `java.nio.channels.UnsupportedAddressTypeException` on Linux, this is typically due to IPv6 address resolution issues.

#### **Quick Solution**
```java
import org.npci.rustyclient.client.util.NetworkCompatibilityUtils;

public class YourApplication {
    static {
        // Configure Linux networking early in your application
        NetworkCompatibilityUtils.configureLinuxNetworking();
    }

    public static void main(String[] args) {
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
            .addPrimaryNode("127.0.0.1", 50051)  // Use IP instead of "localhost"
            .preferIpv4(true)  // Prefer IPv4 addresses
            .build();

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Your code here
        }
    }
}
```

#### **JVM Arguments Solution**
```bash
java -Djava.net.preferIPv4Stack=true \
     -Djava.net.preferIPv6Addresses=false \
     your.application.Main
```

#### **Best Practices for Linux**
- Use IP addresses instead of hostnames when possible
- Call `NetworkCompatibilityUtils.configureLinuxNetworking()` early in your application
- Enable `preferIpv4(true)` in your client configuration
- Use shorter timeouts for faster failure detection

For detailed troubleshooting, see [docs/LINUX_TROUBLESHOOTING.md](docs/LINUX_TROUBLESHOOTING.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
