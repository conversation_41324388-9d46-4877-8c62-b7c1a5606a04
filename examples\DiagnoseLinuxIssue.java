import org.npci.rustyclient.client.util.NetworkCompatibilityUtils;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;

/**
 * Diagnostic tool to help troubleshoot Linux networking issues.
 * Run this before trying to use the RustyCluster client.
 */
public class DiagnoseLinuxIssue {
    
    public static void main(String[] args) {
        System.out.println("=== RustyCluster Linux Compatibility Diagnostic ===\n");
        
        // Step 1: Show current system info
        System.out.println("1. System Information:");
        System.out.println("   OS: " + System.getProperty("os.name"));
        System.out.println("   Java Version: " + System.getProperty("java.version"));
        System.out.println("   Is Linux: " + NetworkCompatibilityUtils.isLinux());
        System.out.println();
        
        // Step 2: Show current network properties BEFORE configuration
        System.out.println("2. Network Properties BEFORE configuration:");
        logNetworkProperties();
        System.out.println();
        
        // Step 3: Apply Linux configuration
        System.out.println("3. Applying Linux network configuration...");
        NetworkCompatibilityUtils.configureLinuxNetworking();
        System.out.println();
        
        // Step 4: Show network properties AFTER configuration
        System.out.println("4. Network Properties AFTER configuration:");
        logNetworkProperties();
        System.out.println();
        
        // Step 5: Test address resolution
        System.out.println("5. Testing Address Resolution:");
        testAddressResolution("localhost");
        testAddressResolution("127.0.0.1");
        if (args.length > 0) {
            testAddressResolution(args[0]);
        }
        System.out.println();
        
        // Step 6: Test socket address creation
        System.out.println("6. Testing Socket Address Creation:");
        testSocketAddressCreation("localhost", 50051);
        testSocketAddressCreation("127.0.0.1", 50051);
        if (args.length > 1) {
            try {
                int port = Integer.parseInt(args[1]);
                testSocketAddressCreation(args[0], port);
            } catch (NumberFormatException e) {
                System.out.println("   Invalid port number: " + args[1]);
            }
        }
        System.out.println();
        
        // Step 7: Validation
        System.out.println("7. Configuration Validation:");
        boolean isValid = NetworkCompatibilityUtils.validateNetworkConfiguration();
        System.out.println("   Configuration is " + (isValid ? "VALID" : "INVALID"));
        System.out.println();
        
        // Step 8: Recommendations
        System.out.println("8. Platform Recommendations:");
        System.out.println(NetworkCompatibilityUtils.getPlatformRecommendations());
        
        System.out.println("=== Diagnostic Complete ===");
        System.out.println("\nIf you're still getting UnsupportedAddressTypeException:");
        System.out.println("1. Make sure to call NetworkCompatibilityUtils.configureLinuxNetworking() BEFORE creating any gRPC channels");
        System.out.println("2. Use IP addresses (127.0.0.1) instead of hostnames (localhost) in your configuration");
        System.out.println("3. Set preferIpv4(true) in your RustyClusterClientConfig");
        System.out.println("4. Try running with JVM args: -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false");
    }
    
    private static void logNetworkProperties() {
        System.out.println("   java.net.preferIPv4Stack: " + System.getProperty("java.net.preferIPv4Stack"));
        System.out.println("   java.net.preferIPv6Addresses: " + System.getProperty("java.net.preferIPv6Addresses"));
        System.out.println("   networkaddress.cache.ttl: " + System.getProperty("networkaddress.cache.ttl"));
        System.out.println("   networkaddress.cache.negative.ttl: " + System.getProperty("networkaddress.cache.negative.ttl"));
        System.out.println("   java.net.useSystemProxies: " + System.getProperty("java.net.useSystemProxies"));
    }
    
    private static void testAddressResolution(String hostname) {
        try {
            System.out.println("   Resolving '" + hostname + "':");
            InetAddress[] addresses = InetAddress.getAllByName(hostname);
            for (int i = 0; i < addresses.length; i++) {
                InetAddress addr = addresses[i];
                System.out.println("     [" + i + "] " + addr.getHostAddress() + 
                                 " (IPv" + (addr instanceof java.net.Inet4Address ? "4" : "6") + ")");
            }
            
            // Test preferred address selection
            InetAddress preferred = InetAddress.getByName(hostname);
            System.out.println("     Preferred: " + preferred.getHostAddress() + 
                             " (IPv" + (preferred instanceof java.net.Inet4Address ? "4" : "6") + ")");
                             
        } catch (UnknownHostException e) {
            System.out.println("     ERROR: " + e.getMessage());
        }
    }
    
    private static void testSocketAddressCreation(String hostname, int port) {
        try {
            System.out.println("   Creating socket address for " + hostname + ":" + port);
            
            // Test direct creation
            InetSocketAddress socketAddress = new InetSocketAddress(hostname, port);
            System.out.println("     Success: " + socketAddress.getAddress().getHostAddress() + ":" + socketAddress.getPort());
            
            // Test with resolved address
            InetAddress address = InetAddress.getByName(hostname);
            InetSocketAddress resolvedSocketAddress = new InetSocketAddress(address, port);
            System.out.println("     Resolved: " + resolvedSocketAddress.getAddress().getHostAddress() + ":" + resolvedSocketAddress.getPort());
            
        } catch (Exception e) {
            System.out.println("     ERROR: " + e.getClass().getSimpleName() + " - " + e.getMessage());
        }
    }
}
