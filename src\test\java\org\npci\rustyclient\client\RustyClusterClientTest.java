package org.npci.rustyclient.client;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.npci.rustyclient.client.BatchOperationBuilder;
import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import rustycluster.KeyValueServiceGrpc;
import rustycluster.Rustycluster;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RustyClusterClientTest {

    @Mock
    private ConnectionManager connectionManager;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub stub;

    private RustyClusterClient client;

    @BeforeEach
    void setUp() throws Exception {
        // Create a test configuration
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .build();

        // Create a client with a mocked ConnectionManager
        client = new RustyClusterClient(config, connectionManager);

        // Default behavior for ConnectionManager (lenient to avoid unnecessary stubbing warnings)
        // Handle both the old single-parameter method and the new two-parameter method
        lenient().when(connectionManager.executeWithFailover(any())).thenAnswer(invocation -> {
            ConnectionManager.ClientOperation<?> operation = invocation.getArgument(0);
            return operation.execute(stub);
        });

        lenient().when(connectionManager.executeWithFailover(any(), any(OperationType.class))).thenAnswer(invocation -> {
            ConnectionManager.ClientOperation<?> operation = invocation.getArgument(0);
            return operation.execute(stub);
        });

        lenient().when(connectionManager.executeWithFailoverSilent(any())).thenAnswer(invocation -> {
            ConnectionManager.ClientOperation<?> operation = invocation.getArgument(0);
            return operation.execute(stub);
        });
    }

    @Test
    @DisplayName("Should set a key-value pair")
    void shouldSetKeyValuePair() {
        // Given
        Rustycluster.SetResponse response = Rustycluster.SetResponse.newBuilder().setSuccess(true).build();
        when(stub.set(any(Rustycluster.SetRequest.class))).thenReturn(response);

        // When
        boolean result = client.set("key", "value");

        // Then
        assertThat(result).isTrue();
        verify(stub).set(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should set a key-value pair with skip replication")
    void shouldSetKeyValuePairWithSkipReplication() {
        // Given
        Rustycluster.SetResponse response = Rustycluster.SetResponse.newBuilder().setSuccess(true).build();
        when(stub.set(any(Rustycluster.SetRequest.class))).thenReturn(response);

        // When
        boolean result = client.set("key", "value", true);

        // Then
        assertThat(result).isTrue();
        verify(stub).set(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should get a value by key")
    void shouldGetValueByKey() {
        // Given
        Rustycluster.GetResponse response = Rustycluster.GetResponse.newBuilder()
                .setValue("value")
                .setFound(true)
                .build();
        when(stub.get(any(Rustycluster.GetRequest.class))).thenReturn(response);

        // When
        String result = client.get("key");

        // Then
        assertThat(result).isEqualTo("value");
        verify(stub).get(argThat(request -> request.getKey().equals("key")));
    }

    @Test
    @DisplayName("Should return null when key is not found")
    void shouldReturnNullWhenKeyNotFound() {
        // Given
        Rustycluster.GetResponse response = Rustycluster.GetResponse.newBuilder()
                .setFound(false)
                .build();
        when(stub.get(any(Rustycluster.GetRequest.class))).thenReturn(response);

        // When
        String result = client.get("key");

        // Then
        assertThat(result).isNull();
        verify(stub).get(argThat(request -> request.getKey().equals("key")));
    }

    @Test
    @DisplayName("Should delete a key")
    void shouldDeleteKey() {
        // Given
        Rustycluster.DeleteResponse response = Rustycluster.DeleteResponse.newBuilder().setSuccess(true).build();
        when(stub.delete(any(Rustycluster.DeleteRequest.class))).thenReturn(response);

        // When
        boolean result = client.delete("key");

        // Then
        assertThat(result).isTrue();
        verify(stub).delete(argThat(request ->
                request.getKey().equals("key") &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should set a key with expiration")
    void shouldSetKeyWithExpiration() {
        // Given
        Rustycluster.SetExResponse response = Rustycluster.SetExResponse.newBuilder().setSuccess(true).build();
        when(stub.setEx(any(Rustycluster.SetExRequest.class))).thenReturn(response);

        // When
        boolean result = client.setEx("key", "value", 60);

        // Then
        assertThat(result).isTrue();
        verify(stub).setEx(argThat(request ->
                request.getKey().equals("key") &&
                request.getValue().equals("value") &&
                request.getTtl() == 60 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should increment a numeric value")
    void shouldIncrementNumericValue() {
        // Given
        Rustycluster.IncrByResponse response = Rustycluster.IncrByResponse.newBuilder().setNewValue(11).build();
        when(stub.incrBy(any(Rustycluster.IncrByRequest.class))).thenReturn(response);

        // When
        long result = client.incrBy("counter", 1);

        // Then
        assertThat(result).isEqualTo(11);
        verify(stub).incrBy(argThat(request ->
                request.getKey().equals("counter") &&
                request.getValue() == 1 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should get all hash fields")
    void shouldGetAllHashFields() {
        // Given
        Rustycluster.HGetAllResponse response = Rustycluster.HGetAllResponse.newBuilder()
                .putFields("name", "John")
                .putFields("age", "30")
                .build();
        when(stub.hGetAll(any(Rustycluster.HGetAllRequest.class))).thenReturn(response);

        // When
        Map<String, String> result = client.hGetAll("user:1");

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).containsEntry("name", "John");
        assertThat(result).containsEntry("age", "30");
        verify(stub).hGetAll(argThat(request -> request.getKey().equals("user:1")));
    }

    @Test
    @DisplayName("Should execute batch operations")
    void shouldExecuteBatchOperations() {
        // Given
        Rustycluster.BatchWriteResponse response = Rustycluster.BatchWriteResponse.newBuilder()
                .setSuccess(true)
                .addAllOperationResults(List.of(true, true, true))
                .build();
        when(stub.batchWrite(any(Rustycluster.BatchWriteRequest.class))).thenReturn(response);

        // When
        List<Rustycluster.BatchOperation> operations = new BatchOperationBuilder()
                .addSet("key1", "value1")
                .addSet("key2", "value2")
                .addSetEx("key3", "value3", 60)
                .build();
        List<Boolean> results = client.batchWrite(operations);

        // Then
        assertThat(results).hasSize(3);
        assertThat(results).containsOnly(true);
        verify(stub).batchWrite(argThat(request ->
                request.getOperationsCount() == 3 &&
                !request.getSkipReplication()));
    }

    @Test
    @DisplayName("Should close connection manager when closed")
    void shouldCloseConnectionManagerWhenClosed() {
        // When
        client.close();

        // Then
        verify(connectionManager).close();
    }

    @Test
    @DisplayName("Test new method signatures exist in RustyClusterClient")
    void testSyncClientMethodSignatures() throws Exception {
        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test HMSET method signatures
        Method hMSetMethod1 = clientClass.getMethod("hMSet", String.class, Map.class);
        assertNotNull(hMSetMethod1, "hMSet(String, Map) method should exist");
        assertEquals(boolean.class, hMSetMethod1.getReturnType(), "hMSet should return boolean");

        Method hMSetMethod2 = clientClass.getMethod("hMSet", String.class, Map.class, boolean.class);
        assertNotNull(hMSetMethod2, "hMSet(String, Map, boolean) method should exist");
        assertEquals(boolean.class, hMSetMethod2.getReturnType(), "hMSet with skipReplication should return boolean");

        // Test HEXISTS method signature
        Method hExistsMethod = clientClass.getMethod("hExists", String.class, String.class);
        assertNotNull(hExistsMethod, "hExists(String, String) method should exist");
        assertEquals(boolean.class, hExistsMethod.getReturnType(), "hExists should return boolean");

        // Test EXISTS method signature
        Method existsMethod = clientClass.getMethod("exists", String.class);
        assertNotNull(existsMethod, "exists(String) method should exist");
        assertEquals(boolean.class, existsMethod.getReturnType(), "exists should return boolean");

        // Test SETNX method signatures
        Method setNXMethod1 = clientClass.getMethod("setNX", String.class, String.class);
        assertNotNull(setNXMethod1, "setNX(String, String) method should exist");
        assertEquals(boolean.class, setNXMethod1.getReturnType(), "setNX should return boolean");

        Method setNXMethod2 = clientClass.getMethod("setNX", String.class, String.class, boolean.class);
        assertNotNull(setNXMethod2, "setNX(String, String, boolean) method should exist");
        assertEquals(boolean.class, setNXMethod2.getReturnType(), "setNX with skipReplication should return boolean");

        // Test LoadScript method signature
        Method loadScriptMethod = clientClass.getMethod("loadScript", String.class);
        assertNotNull(loadScriptMethod, "loadScript(String) method should exist");
        assertEquals(String.class, loadScriptMethod.getReturnType(), "loadScript should return String");

        // Test EvalSha method signatures
        Method evalShaMethod1 = clientClass.getMethod("evalSha", String.class, List.class, List.class);
        assertNotNull(evalShaMethod1, "evalSha(String, List, List) method should exist");
        assertEquals(String.class, evalShaMethod1.getReturnType(), "evalSha should return String");

        Method evalShaMethod2 = clientClass.getMethod("evalSha", String.class, List.class, List.class, boolean.class);
        assertNotNull(evalShaMethod2, "evalSha(String, List, List, boolean) method should exist");
        assertEquals(String.class, evalShaMethod2.getReturnType(), "evalSha with skipReplication should return String");

        // Test HealthCheck method signature
        Method healthCheckMethod = clientClass.getMethod("healthCheck");
        assertNotNull(healthCheckMethod, "healthCheck() method should exist");
        assertEquals(boolean.class, healthCheckMethod.getReturnType(), "healthCheck should return boolean");

        // Test Ping method signature
        Method pingMethod = clientClass.getMethod("ping");
        assertNotNull(pingMethod, "ping() method should exist");
        assertEquals(boolean.class, pingMethod.getReturnType(), "ping should return boolean");

        // Test new hash methods
        Method hDelMethod = clientClass.getMethod("hDel", String.class, String[].class);
        assertNotNull(hDelMethod, "hDel(String, String[]) method should exist");
        assertEquals(int.class, hDelMethod.getReturnType(), "hDel should return int");

        Method hScanMethod = clientClass.getMethod("hScan", String.class, long.class, String.class, Integer.class);
        assertNotNull(hScanMethod, "hScan(String, long, String, Integer) method should exist");
        assertEquals(org.npci.rustyclient.client.HScanResult.class, hScanMethod.getReturnType(), "hScan should return HScanResult");

        Method hLenMethod = clientClass.getMethod("hLen", String.class);
        assertNotNull(hLenMethod, "hLen(String) method should exist");
        assertEquals(int.class, hLenMethod.getReturnType(), "hLen should return int");

        // Test new set methods
        Method sAddMethod = clientClass.getMethod("sAdd", String.class, String[].class);
        assertNotNull(sAddMethod, "sAdd(String, String[]) method should exist");
        assertEquals(int.class, sAddMethod.getReturnType(), "sAdd should return int");

        Method sMembersMethod = clientClass.getMethod("sMembers", String.class);
        assertNotNull(sMembersMethod, "sMembers(String) method should exist");
        assertEquals(List.class, sMembersMethod.getReturnType(), "sMembers should return List");

        // Test new key methods
        Method delMultipleMethod = clientClass.getMethod("delMultiple", String[].class);
        assertNotNull(delMultipleMethod, "delMultiple(String[]) method should exist");
        assertEquals(int.class, delMultipleMethod.getReturnType(), "delMultiple should return int");
    }

    @Test
    @DisplayName("Test async method signatures exist in main RustyClusterClient")
    void testAsyncClientMethodSignatures() throws Exception {
        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test HMSET async method signatures
        Method hMSetAsyncMethod1 = clientClass.getMethod("hMSetAsync", String.class, Map.class);
        assertNotNull(hMSetAsyncMethod1, "hMSetAsync(String, Map) method should exist");
        assertEquals(CompletableFuture.class, hMSetAsyncMethod1.getReturnType(), "hMSetAsync should return CompletableFuture");

        Method hMSetAsyncMethod2 = clientClass.getMethod("hMSetAsync", String.class, Map.class, boolean.class);
        assertNotNull(hMSetAsyncMethod2, "hMSetAsync(String, Map, boolean) method should exist");
        assertEquals(CompletableFuture.class, hMSetAsyncMethod2.getReturnType(), "hMSetAsync with skipReplication should return CompletableFuture");

        // Test HEXISTS async method signature
        Method hExistsAsyncMethod = clientClass.getMethod("hExistsAsync", String.class, String.class);
        assertNotNull(hExistsAsyncMethod, "hExistsAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, hExistsAsyncMethod.getReturnType(), "hExistsAsync should return CompletableFuture");

        // Test EXISTS async method signature
        Method existsAsyncMethod = clientClass.getMethod("existsAsync", String.class);
        assertNotNull(existsAsyncMethod, "existsAsync(String) method should exist");
        assertEquals(CompletableFuture.class, existsAsyncMethod.getReturnType(), "existsAsync should return CompletableFuture");

        // Test SETNX async method signatures
        Method setNXAsyncMethod1 = clientClass.getMethod("setNXAsync", String.class, String.class);
        assertNotNull(setNXAsyncMethod1, "setNXAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, setNXAsyncMethod1.getReturnType(), "setNXAsync should return CompletableFuture");

        Method setNXAsyncMethod2 = clientClass.getMethod("setNXAsync", String.class, String.class, boolean.class);
        assertNotNull(setNXAsyncMethod2, "setNXAsync(String, String, boolean) method should exist");
        assertEquals(CompletableFuture.class, setNXAsyncMethod2.getReturnType(), "setNXAsync with skipReplication should return CompletableFuture");

        // Test LoadScript async method signature
        Method loadScriptAsyncMethod = clientClass.getMethod("loadScriptAsync", String.class);
        assertNotNull(loadScriptAsyncMethod, "loadScriptAsync(String) method should exist");
        assertEquals(CompletableFuture.class, loadScriptAsyncMethod.getReturnType(), "loadScriptAsync should return CompletableFuture");

        // Test EvalSha async method signatures
        Method evalShaAsyncMethod1 = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class);
        assertNotNull(evalShaAsyncMethod1, "evalShaAsync(String, List, List) method should exist");
        assertEquals(CompletableFuture.class, evalShaAsyncMethod1.getReturnType(), "evalShaAsync should return CompletableFuture");

        Method evalShaAsyncMethod2 = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class, boolean.class);
        assertNotNull(evalShaAsyncMethod2, "evalShaAsync(String, List, List, boolean) method should exist");
        assertEquals(CompletableFuture.class, evalShaAsyncMethod2.getReturnType(), "evalShaAsync with skipReplication should return CompletableFuture");

        // Test HealthCheck async method signature
        Method healthCheckAsyncMethod = clientClass.getMethod("healthCheckAsync");
        assertNotNull(healthCheckAsyncMethod, "healthCheckAsync() method should exist");
        assertEquals(CompletableFuture.class, healthCheckAsyncMethod.getReturnType(), "healthCheckAsync should return CompletableFuture");

        // Test Ping async method signature
        Method pingAsyncMethod = clientClass.getMethod("pingAsync");
        assertNotNull(pingAsyncMethod, "pingAsync() method should exist");
        assertEquals(CompletableFuture.class, pingAsyncMethod.getReturnType(), "pingAsync should return CompletableFuture");

        // Test basic async methods
        Method setAsyncMethod = clientClass.getMethod("setAsync", String.class, String.class);
        assertNotNull(setAsyncMethod, "setAsync(String, String) method should exist");
        assertEquals(CompletableFuture.class, setAsyncMethod.getReturnType(), "setAsync should return CompletableFuture");

        Method getAsyncMethod = clientClass.getMethod("getAsync", String.class);
        assertNotNull(getAsyncMethod, "getAsync(String) method should exist");
        assertEquals(CompletableFuture.class, getAsyncMethod.getReturnType(), "getAsync should return CompletableFuture");

        Method deleteAsyncMethod = clientClass.getMethod("deleteAsync", String.class);
        assertNotNull(deleteAsyncMethod, "deleteAsync(String) method should exist");
        assertEquals(CompletableFuture.class, deleteAsyncMethod.getReturnType(), "deleteAsync should return CompletableFuture");

        // Test new async hash methods
        Method hDelAsyncMethod = clientClass.getMethod("hDelAsync", String.class, String[].class);
        assertNotNull(hDelAsyncMethod, "hDelAsync(String, String[]) method should exist");
        assertEquals(CompletableFuture.class, hDelAsyncMethod.getReturnType(), "hDelAsync should return CompletableFuture");

        Method hScanAsyncMethod = clientClass.getMethod("hScanAsync", String.class, long.class, String.class, Integer.class);
        assertNotNull(hScanAsyncMethod, "hScanAsync(String, long, String, Integer) method should exist");
        assertEquals(CompletableFuture.class, hScanAsyncMethod.getReturnType(), "hScanAsync should return CompletableFuture");

        Method hLenAsyncMethod = clientClass.getMethod("hLenAsync", String.class);
        assertNotNull(hLenAsyncMethod, "hLenAsync(String) method should exist");
        assertEquals(CompletableFuture.class, hLenAsyncMethod.getReturnType(), "hLenAsync should return CompletableFuture");

        // Test new async set methods
        Method sAddAsyncMethod = clientClass.getMethod("sAddAsync", String.class, String[].class);
        assertNotNull(sAddAsyncMethod, "sAddAsync(String, String[]) method should exist");
        assertEquals(CompletableFuture.class, sAddAsyncMethod.getReturnType(), "sAddAsync should return CompletableFuture");

        Method sMembersAsyncMethod = clientClass.getMethod("sMembersAsync", String.class);
        assertNotNull(sMembersAsyncMethod, "sMembersAsync(String) method should exist");
        assertEquals(CompletableFuture.class, sMembersAsyncMethod.getReturnType(), "sMembersAsync should return CompletableFuture");

        // Test new async key methods
        Method delMultipleAsyncMethod = clientClass.getMethod("delMultipleAsync", String[].class);
        assertNotNull(delMultipleAsyncMethod, "delMultipleAsync(String[]) method should exist");
        assertEquals(CompletableFuture.class, delMultipleAsyncMethod.getReturnType(), "delMultipleAsync should return CompletableFuture");
    }

    @Test
    @DisplayName("Test BatchOperationBuilder new methods exist")
    void testBatchOperationBuilderMethods() throws Exception {
        Class<BatchOperationBuilder> builderClass = BatchOperationBuilder.class;

        // Test addHMSet method signature
        Method addHMSetMethod = builderClass.getMethod("addHMSet", String.class, Map.class);
        assertNotNull(addHMSetMethod, "addHMSet(String, Map) method should exist");
        assertEquals(BatchOperationBuilder.class, addHMSetMethod.getReturnType(), "addHMSet should return BatchOperationBuilder");

        // Test addSetNX method signature
        Method addSetNXMethod = builderClass.getMethod("addSetNX", String.class, String.class);
        assertNotNull(addSetNXMethod, "addSetNX(String, String) method should exist");
        assertEquals(BatchOperationBuilder.class, addSetNXMethod.getReturnType(), "addSetNX should return BatchOperationBuilder");

        // Test addLoadScript method signature
        Method addLoadScriptMethod = builderClass.getMethod("addLoadScript", String.class);
        assertNotNull(addLoadScriptMethod, "addLoadScript(String) method should exist");
        assertEquals(BatchOperationBuilder.class, addLoadScriptMethod.getReturnType(), "addLoadScript should return BatchOperationBuilder");

        // Test addEvalSha method signature
        Method addEvalShaMethod = builderClass.getMethod("addEvalSha", String.class, List.class, List.class);
        assertNotNull(addEvalShaMethod, "addEvalSha(String, List, List) method should exist");
        assertEquals(BatchOperationBuilder.class, addEvalShaMethod.getReturnType(), "addEvalSha should return BatchOperationBuilder");
    }

    @Test
    @DisplayName("Should return null when all nodes are unavailable for read operations")
    void shouldReturnNullWhenAllNodesUnavailableForReads() {
        // Given - reset the mock to override the default behavior for this test
        reset(connectionManager);
        when(connectionManager.executeWithFailoverSilent(any())).thenReturn(null);

        // When
        String getResult = client.get("key");
        String hGetResult = client.hGet("hash", "field");
        boolean existsResult = client.exists("key");
        boolean hExistsResult = client.hExists("hash", "field");

        // Then - all read operations should return null/false instead of throwing exceptions
        assertThat(getResult).isNull();
        assertThat(hGetResult).isNull();
        assertThat(existsResult).isFalse();
        assertThat(hExistsResult).isFalse();

        // Verify that the silent failover method was called for read operations
        verify(connectionManager, times(4)).executeWithFailoverSilent(any());
    }

    @Test
    @DisplayName("Test method implementation status")
    void testMethodImplementationStatus() {
        // This test verifies that the methods exist and can be called
        // It doesn't test actual functionality since that requires a server

        Class<RustyClusterClient> clientClass = RustyClusterClient.class;

        // Test that LoadScript returns null (temporary implementation)
        assertDoesNotThrow(() -> {
            // Just verify the method signature exists and can be called
            Method loadScriptMethod = clientClass.getMethod("loadScript", String.class);
            assertNotNull(loadScriptMethod);
        }, "LoadScript method should exist and be callable");

        // Test that EvalSha returns null (temporary implementation)
        assertDoesNotThrow(() -> {
            Method evalShaMethod = clientClass.getMethod("evalSha", String.class, List.class, List.class);
            assertNotNull(evalShaMethod);
        }, "EvalSha method should exist and be callable");

        // Test async versions in main client
        assertDoesNotThrow(() -> {
            Method loadScriptAsyncMethod = clientClass.getMethod("loadScriptAsync", String.class);
            assertNotNull(loadScriptAsyncMethod);
        }, "LoadScriptAsync method should exist and be callable in main client");

        assertDoesNotThrow(() -> {
            Method evalShaAsyncMethod = clientClass.getMethod("evalShaAsync", String.class, List.class, List.class);
            assertNotNull(evalShaAsyncMethod);
        }, "EvalShaAsync method should exist and be callable in main client");

        // Test that both sync and async methods coexist
        assertDoesNotThrow(() -> {
            Method setMethod = clientClass.getMethod("set", String.class, String.class);
            Method setAsyncMethod = clientClass.getMethod("setAsync", String.class, String.class);
            assertNotNull(setMethod);
            assertNotNull(setAsyncMethod);
        }, "Both sync and async set methods should exist in main client");

        assertDoesNotThrow(() -> {
            Method getMethod = clientClass.getMethod("get", String.class);
            Method getAsyncMethod = clientClass.getMethod("getAsync", String.class);
            assertNotNull(getMethod);
            assertNotNull(getAsyncMethod);
        }, "Both sync and async get methods should exist in main client");
    }
}
