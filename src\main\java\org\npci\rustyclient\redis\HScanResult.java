package org.npci.rustyclient.redis;

import java.util.Map;

/**
 * Result of a Redis hash scan operation.
 * Contains the next cursor for pagination and the field-value pairs found.
 */
public class HScanResult {
    private final String nextCursor;
    private final Map<String, String> fields;

    /**
     * Create a new HScanResult.
     *
     * @param nextCursor The next cursor for pagination ("0" if scan is complete)
     * @param fields     The field-value pairs found in this scan
     */
    public HScanResult(String nextCursor, Map<String, String> fields) {
        this.nextCursor = nextCursor;
        this.fields = fields;
    }

    /**
     * Get the next cursor for pagination.
     *
     * @return The next cursor ("0" if scan is complete)
     */
    public String getNextCursor() {
        return nextCursor;
    }

    /**
     * Get the field-value pairs found in this scan.
     *
     * @return Map of field names to values
     */
    public Map<String, String> getFields() {
        return fields;
    }

    /**
     * Check if the scan is complete.
     *
     * @return True if there are no more results to scan
     */
    public boolean isComplete() {
        return "0".equals(nextCursor);
    }

    @Override
    public String toString() {
        return "HScanResult{" +
                "nextCursor='" + nextCursor + '\'' +
                ", fields=" + fields +
                '}';
    }
}
