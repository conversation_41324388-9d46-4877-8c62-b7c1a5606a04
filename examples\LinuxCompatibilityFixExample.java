import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.util.NetworkCompatibilityUtils;

/**
 * Example showing how to properly configure RustyCluster client for Linux compatibility.
 * This example addresses the UnsupportedAddressTypeException issue.
 */
public class LinuxCompatibilityFixExample {
    
    public static void main(String[] args) {
        System.out.println("=== RustyCluster Linux Compatibility Fix Example ===\n");
        
        // CRITICAL: Configure networking BEFORE creating any gRPC channels
        // This MUST be the first thing you do in your application
        System.out.println("Step 1: Configuring network compatibility...");
        NetworkCompatibilityUtils.configureLinuxNetworking();
        
        // Optional: If the above doesn't work, try the force configuration
        // NetworkCompatibilityUtils.forceConfigureNetworking();
        
        // Log current configuration for debugging
        NetworkCompatibilityUtils.logNetworkProperties();
        System.out.println();
        
        // Step 2: Create client configuration with Linux-friendly settings
        System.out.println("Step 2: Creating client configuration...");
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                // IMPORTANT: Use IP addresses instead of hostnames when possible
                .addPrimaryNode("127.0.0.1", 50051)    // NOT "localhost"
                .addSecondaryNode("127.0.0.1", 50052)  // NOT "localhost"
                
                // CRITICAL: Enable IPv4 preference
                .preferIpv4(true)
                
                // Optional: Configure timeouts for faster failure detection
                .connectionTimeout(5, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(3, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(3, java.util.concurrent.TimeUnit.SECONDS)
                
                // Optional: Reduce retries for faster failover
                .maxRetries(2)
                
                .build();
        
        System.out.println("Configuration created with preferIpv4: " + config.isPreferIpv4());
        System.out.println();
        
        // Step 3: Create and use the client
        System.out.println("Step 3: Creating RustyCluster client...");
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            System.out.println("Client created successfully!");
            
            // Test basic operations
            System.out.println("\nStep 4: Testing basic operations...");
            
            // Test SET operation
            try {
                client.set("test-key", "test-value");
                System.out.println("✓ SET operation successful");
            } catch (Exception e) {
                System.err.println("✗ SET operation failed: " + e.getMessage());
                e.printStackTrace();
            }
            
            // Test GET operation
            try {
                String value = client.get("test-key");
                System.out.println("✓ GET operation successful, value: " + value);
            } catch (Exception e) {
                System.err.println("✗ GET operation failed: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("Failed to create or use RustyCluster client:");
            System.err.println("Error: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            e.printStackTrace();
            
            System.err.println("\n=== TROUBLESHOOTING GUIDE ===");
            System.err.println("If you're getting UnsupportedAddressTypeException:");
            System.err.println("1. Make sure you called NetworkCompatibilityUtils.configureLinuxNetworking() FIRST");
            System.err.println("2. Use IP addresses (127.0.0.1) instead of hostnames (localhost)");
            System.err.println("3. Set preferIpv4(true) in your configuration");
            System.err.println("4. Try running with JVM args: -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false");
            System.err.println("5. Check if your gRPC server is actually running and accessible");
            System.err.println("6. Try the DiagnoseLinuxIssue tool first");
        }
        
        System.out.println("\n=== Example Complete ===");
    }
}

/**
 * Alternative approach using static initialization.
 * This ensures network configuration is applied as early as possible.
 */
class LinuxCompatibilityStaticExample {
    
    // CRITICAL: Configure networking in static initializer
    static {
        System.out.println("Applying Linux network compatibility configuration...");
        NetworkCompatibilityUtils.configureLinuxNetworking();
    }
    
    public static void main(String[] args) {
        System.out.println("=== Static Initialization Example ===");
        
        // Network configuration is already applied by static initializer
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("127.0.0.1", 50051)
                .preferIpv4(true)
                .build();
        
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            System.out.println("Client created successfully with static configuration!");
            
            // Your application logic here
            client.set("static-test", "value");
            String value = client.get("static-test");
            System.out.println("Retrieved value: " + value);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

/**
 * Example for Spring Boot or other framework applications.
 */
class SpringBootCompatibilityExample {
    
    // For Spring Boot, you can use @PostConstruct or ApplicationRunner
    // @PostConstruct
    public void configureNetworking() {
        NetworkCompatibilityUtils.configureLinuxNetworking();
    }
    
    // Or create a configuration bean
    // @Bean
    public RustyClusterClientConfig rustyClusterConfig() {
        // Ensure networking is configured
        NetworkCompatibilityUtils.configureLinuxNetworking();
        
        return RustyClusterClientConfig.builder()
                .addPrimaryNode("127.0.0.1", 50051)
                .preferIpv4(true)
                .build();
    }
}
