package org.npci.rustyclient.redis;

import org.npci.rustyclient.redis.config.RedisClientConfig;
import org.npci.rustyclient.redis.connection.RedisConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.resps.ScanResult;

/**
 * Main client for interacting with Redis directly.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * <pre>
 * RedisClientConfig config = RedisClientConfig.builder()
 *     .addPrimaryNode("localhost", 6379)
 *     .authentication("username", "password")
 *     .database(0)
 *     .build();
 *
 * try (RedisClient client = new RedisClient(config)) {
 *     // Synchronous operations
 *     client.set("key1", "value1");
 *     String value = client.get("key1");
 *
 *     // Asynchronous operations
 *     CompletableFuture&lt;Boolean&gt; setFuture = client.setAsync("key2", "value2");
 *     CompletableFuture&lt;String&gt; getFuture = client.getAsync("key2");
 * }
 * </pre>
 */
public class RedisClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RedisClient.class);

    private final RedisClientConfig config;
    private final RedisConnectionManager connectionManager;

    /**
     * Create a new RedisClient with the provided configuration.
     *
     * @param config The client configuration
     */
    public RedisClient(RedisClientConfig config) {
        this.config = config;
        this.connectionManager = new RedisConnectionManager(config);
        logger.info("RedisClient initialized with {} nodes", config.getNodes().size());
    }

    // ==================== STRING OPERATIONS ====================

    /**
     * Set a key-value pair.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
        logger.debug("Setting key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.set(key, value);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting key: {}", key, e);
            throw e;
        }
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key The key
     * @param value The value
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttlSeconds) {
        logger.debug("Setting key with expiry: {}, ttl: {}", key, ttlSeconds);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.setex(key, ttlSeconds, value);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting key with expiry: {}", key, e);
            throw e;
        }
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
        logger.debug("Setting key if not exists: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.setnx(key, value);
                return result != null && result == 1;
            });
        } catch (Exception e) {
            logger.error("Error setting key if not exists: {}", key, e);
            throw e;
        }
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found or if all nodes are unavailable
     */
    public String get(String key) {
        logger.debug("Getting key: {}", key);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.get(key));
    }

    /**
     * Delete a key.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
        logger.debug("Deleting key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.del(key);
                return result != null && result > 0;
            });
        } catch (Exception e) {
            logger.error("Error deleting key: {}", key, e);
            throw e;
        }
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise or if all nodes are unavailable
     */
    public boolean exists(String key) {
        logger.debug("Checking if key exists: {}", key);
        
        Boolean result = connectionManager.executeWithFailoverSilent(connection -> 
            connection.exists(key));
        
        return result != null && result;
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key The key
     * @param ttlSeconds The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean expire(String key, long ttlSeconds) {
        logger.debug("Setting expiry on key: {}, ttl: {}", key, ttlSeconds);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.expire(key, ttlSeconds);
                return result != null && result == 1;
            });
        } catch (Exception e) {
            logger.error("Error setting expiry on key: {}", key, e);
            throw e;
        }
    }

    /**
     * Increment a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long incr(String key) {
        logger.debug("Incrementing key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.incr(key));
        } catch (Exception e) {
            logger.error("Error incrementing key: {}", key, e);
            throw e;
        }
    }

    /**
     * Increment a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
        logger.debug("Incrementing key: {} by {}", key, value);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.incrBy(key, value));
        } catch (Exception e) {
            logger.error("Error incrementing key: {} by {}", key, value, e);
            throw e;
        }
    }

    /**
     * Decrement a numeric value.
     *
     * @param key The key
     * @return The new value
     */
    public long decr(String key) {
        logger.debug("Decrementing key: {}", key);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.decr(key));
        } catch (Exception e) {
            logger.error("Error decrementing key: {}", key, e);
            throw e;
        }
    }

    /**
     * Decrement a numeric value by a specific amount.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
        logger.debug("Decrementing key: {} by {}", key, value);
        
        try {
            return connectionManager.executeWithFailover(connection -> 
                connection.decrBy(key, value));
        } catch (Exception e) {
            logger.error("Error decrementing key: {} by {}", key, value, e);
            throw e;
        }
    }

    // ==================== HASH OPERATIONS ====================

    /**
     * Set a field in a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
        logger.debug("Setting hash field: {}.{}", key, field);
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.hset(key, field, value);
                return result != null && result >= 0;
            });
        } catch (Exception e) {
            logger.error("Error setting hash field: {}.{}", key, field, e);
            throw e;
        }
    }

    /**
     * Set multiple fields in a hash.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map<String, String> fields) {
        logger.debug("Setting multiple hash fields for key: {}, fields count: {}", key, fields.size());
        
        try {
            return connectionManager.executeWithFailover(connection -> {
                String result = connection.hmset(key, fields);
                return "OK".equals(result);
            });
        } catch (Exception e) {
            logger.error("Error setting multiple hash fields for key: {}", key, e);
            throw e;
        }
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found or if all nodes are unavailable
     */
    public String hGet(String key, String field) {
        logger.debug("Getting hash field: {}.{}", key, field);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.hget(key, field));
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map<String, String> hGetAll(String key) {
        logger.debug("Getting all hash fields for key: {}", key);
        
        return connectionManager.executeWithFailoverSilent(connection -> 
            connection.hgetAll(key));
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise or if all nodes are unavailable
     */
    public boolean hExists(String key, String field) {
        logger.debug("Checking if hash field exists: {}.{}", key, field);
        
        Boolean result = connectionManager.executeWithFailoverSilent(connection -> 
            connection.hexists(key, field));
        
        return result != null && result;
    }

    /**
     * Delete a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if successful, false otherwise
     */
    public boolean hDel(String key, String field) {
        logger.debug("Deleting hash field: {}.{}", key, field);

        try {
            return connectionManager.executeWithFailover(connection -> {
                Long result = connection.hdel(key, field);
                return result != null && result > 0;
            });
        } catch (Exception e) {
            logger.error("Error deleting hash field: {}.{}", key, field, e);
            throw e;
        }
    }

    /**
     * Increment a numeric field in a hash by 1.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The new value after increment
     */
    public long hIncr(String key, String field) {
        logger.debug("Incrementing hash field: {}.{}", key, field);

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.hincrBy(key, field, 1));
        } catch (Exception e) {
            logger.error("Error incrementing hash field: {}.{}", key, field, e);
            throw e;
        }
    }

    /**
     * Increment a numeric field in a hash by a specific amount.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value after increment
     */
    public long hIncrBy(String key, String field, long value) {
        logger.debug("Incrementing hash field: {}.{} by {}", key, field, value);

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.hincrBy(key, field, value));
        } catch (Exception e) {
            logger.error("Error incrementing hash field: {}.{} by {}", key, field, value, e);
            throw e;
        }
    }

    /**
     * Increment a floating-point field in a hash by a specific amount.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value after increment
     */
    public double hIncrByFloat(String key, String field, double value) {
        logger.debug("Incrementing hash field: {}.{} by float {}", key, field, value);

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.hincrByFloat(key, field, value));
        } catch (Exception e) {
            logger.error("Error incrementing hash field: {}.{} by float {}", key, field, value, e);
            throw e;
        }
    }

    /**
     * Delete one or more fields from a hash.
     *
     * @param key    The hash key
     * @param fields The field names to delete
     * @return The number of fields that were deleted
     */
    public long hDel(String key, String... fields) {
        logger.debug("Deleting hash fields: {} from key: {}", String.join(", ", fields), key);

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.hdel(key, fields));
        } catch (Exception e) {
            logger.error("Error deleting hash fields from key: {}", key, e);
            throw e;
        }
    }

    /**
     * Scan hash fields with optional pattern matching.
     *
     * @param key     The hash key
     * @param cursor  The cursor for pagination ("0" to start)
     * @param pattern Optional pattern to match field names (can be null)
     * @param count   Optional hint for number of elements to return (can be null)
     * @return HScanResult containing next cursor and field-value pairs
     */
    public HScanResult hScan(String key, String cursor, String pattern, Integer count) {
        logger.debug("Scanning hash fields for key: {}, cursor: {}, pattern: {}, count: {}",
                     key, cursor, pattern, count);

        try {
            return connectionManager.executeWithFailoverSilent(connection -> {
                ScanParams params = new ScanParams();
                if (pattern != null) {
                    params.match(pattern);
                }
                if (count != null) {
                    params.count(count);
                }

                ScanResult<Map.Entry<String, String>> result = connection.hscan(key, cursor, params);
                if (result == null) {
                    return new HScanResult("0", Map.of());
                }

                Map<String, String> fields = Map.of();
                if (result.getResult() != null) {
                    fields = result.getResult().stream()
                        .collect(java.util.stream.Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue
                        ));
                }

                return new HScanResult(result.getCursor(), fields);
            });
        } catch (Exception e) {
            logger.error("Error scanning hash fields for key: {}", key, e);
            return new HScanResult("0", Map.of());
        }
    }

    /**
     * Scan hash fields with default parameters.
     *
     * @param key    The hash key
     * @param cursor The cursor for pagination ("0" to start)
     * @return HScanResult containing next cursor and field-value pairs
     */
    public HScanResult hScan(String key, String cursor) {
        return hScan(key, cursor, null, null);
    }

    /**
     * Get the number of fields in a hash.
     *
     * @param key The hash key
     * @return The number of fields in the hash, or 0 if key doesn't exist or all nodes are unavailable
     */
    public long hLen(String key) {
        logger.debug("Getting hash length for key: {}", key);

        Long result = connectionManager.executeWithFailoverSilent(connection ->
            connection.hlen(key));

        return result != null ? result : 0;
    }

    // ==================== SET OPERATIONS ====================

    /**
     * Add one or more members to a set.
     *
     * @param key     The set key
     * @param members The members to add
     * @return The number of members that were added (not already in set)
     */
    public long sAdd(String key, String... members) {
        logger.debug("Adding members to set: {} for key: {}", String.join(", ", members), key);

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.sadd(key, members));
        } catch (Exception e) {
            logger.error("Error adding members to set for key: {}", key, e);
            throw e;
        }
    }

    /**
     * Get all members of a set.
     *
     * @param key The set key
     * @return Set of all members in the set, or empty set if key doesn't exist or all nodes are unavailable
     */
    public Set<String> sMembers(String key) {
        logger.debug("Getting all members of set for key: {}", key);

        Set<String> result = connectionManager.executeWithFailoverSilent(connection ->
            connection.smembers(key));

        return result != null ? result : Set.of();
    }

    // ==================== KEY OPERATIONS ====================

    /**
     * Delete multiple keys.
     *
     * @param keys The keys to delete
     * @return The number of keys that were deleted
     */
    public long delMultiple(String... keys) {
        logger.debug("Deleting multiple keys: {}", String.join(", ", keys));

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.del(keys));
        } catch (Exception e) {
            logger.error("Error deleting multiple keys", e);
            throw e;
        }
    }

    // ==================== SCRIPT OPERATIONS ====================

    /**
     * Load a Lua script and return its SHA hash.
     *
     * @param script The Lua script to load
     * @return The SHA hash of the loaded script, or null if failed
     */
    public String scriptLoad(String script) {
        logger.debug("Loading script, length: {}", script.length());

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.scriptLoad(script));
        } catch (Exception e) {
            logger.error("Error loading script", e);
            return null;
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public Object evalSha(String sha, List<String> keys, List<String> args) {
        logger.debug("Executing script with SHA: {}, keys: {}, args: {}", sha, keys.size(), args.size());

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.evalsha(sha, keys, args));
        } catch (Exception e) {
            logger.error("Error executing script with SHA: {}", sha, e);
            return null;
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash (no arguments).
     *
     * @param sha The SHA hash of the script
     * @return The script execution result, or null if failed
     */
    public Object evalSha(String sha) {
        return evalSha(sha, List.of(), List.of());
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash (with keys only).
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @return The script execution result, or null if failed
     */
    public Object evalSha(String sha, List<String> keys) {
        return evalSha(sha, keys, List.of());
    }

    /**
     * Execute a Lua script directly (without pre-loading).
     *
     * @param script The Lua script to execute
     * @param keys   List of keys to pass to the script
     * @param args   List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public Object eval(String script, List<String> keys, List<String> args) {
        logger.debug("Executing script directly, length: {}, keys: {}, args: {}",
                    script.length(), keys.size(), args.size());

        try {
            return connectionManager.executeWithFailover(connection ->
                connection.eval(script, keys, args));
        } catch (Exception e) {
            logger.error("Error executing script directly", e);
            return null;
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Ping the Redis server to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
        logger.debug("Pinging Redis server");

        try {
            String result = connectionManager.executeWithFailover(connection ->
                connection.ping());
            return "PONG".equals(result);
        } catch (Exception e) {
            logger.warn("Ping failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Perform a health check on the Redis cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
        logger.debug("Performing health check");
        return connectionManager.healthCheck();
    }

    /**
     * Get connection pool statistics.
     *
     * @return Pool statistics for all nodes
     */
    public String getPoolStats() {
        return connectionManager.getPoolStats();
    }

    // ==================== ASYNCHRONOUS OPERATIONS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value) {
        return CompletableFuture.supplyAsync(() -> set(key, value));
    }

    /**
     * Set a key-value pair with expiration asynchronously.
     *
     * @param key        The key
     * @param value      The value
     * @param ttlSeconds The time-to-live in seconds
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setExAsync(String key, String value, long ttlSeconds) {
        return CompletableFuture.supplyAsync(() -> setEx(key, value, ttlSeconds));
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture<String> getAsync(String key) {
        return CompletableFuture.supplyAsync(() -> get(key));
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key) {
        return CompletableFuture.supplyAsync(() -> delete(key));
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key to check
     * @return CompletableFuture that completes with true if the key exists, false otherwise
     */
    public CompletableFuture<Boolean> existsAsync(String key) {
        return CompletableFuture.supplyAsync(() -> exists(key));
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hSetAsync(String key, String field, String value) {
        return CompletableFuture.supplyAsync(() -> hSet(key, field, value));
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture<String> hGetAsync(String key, String field) {
        return CompletableFuture.supplyAsync(() -> hGet(key, field));
    }

    /**
     * Get all hash fields asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture<Map<String, String>> hGetAllAsync(String key) {
        return CompletableFuture.supplyAsync(() -> hGetAll(key));
    }

    /**
     * Increment a numeric field in a hash by 1 asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the new value after increment
     */
    public CompletableFuture<Long> hIncrAsync(String key, String field) {
        return CompletableFuture.supplyAsync(() -> hIncr(key, field));
    }

    /**
     * Increment a numeric field in a hash by a specific amount asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return CompletableFuture that completes with the new value after increment
     */
    public CompletableFuture<Long> hIncrByAsync(String key, String field, long value) {
        return CompletableFuture.supplyAsync(() -> hIncrBy(key, field, value));
    }

    /**
     * Increment a floating-point field in a hash by a specific amount asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return CompletableFuture that completes with the new value after increment
     */
    public CompletableFuture<Double> hIncrByFloatAsync(String key, String field, double value) {
        return CompletableFuture.supplyAsync(() -> hIncrByFloat(key, field, value));
    }

    /**
     * Delete one or more fields from a hash asynchronously.
     *
     * @param key    The hash key
     * @param fields The field names to delete
     * @return CompletableFuture that completes with the number of fields that were deleted
     */
    public CompletableFuture<Long> hDelAsync(String key, String... fields) {
        return CompletableFuture.supplyAsync(() -> hDel(key, fields));
    }

    /**
     * Scan hash fields with optional pattern matching asynchronously.
     *
     * @param key     The hash key
     * @param cursor  The cursor for pagination ("0" to start)
     * @param pattern Optional pattern to match field names (can be null)
     * @param count   Optional hint for number of elements to return (can be null)
     * @return CompletableFuture that completes with HScanResult containing next cursor and field-value pairs
     */
    public CompletableFuture<HScanResult> hScanAsync(String key, String cursor, String pattern, Integer count) {
        return CompletableFuture.supplyAsync(() -> hScan(key, cursor, pattern, count));
    }

    /**
     * Scan hash fields with default parameters asynchronously.
     *
     * @param key    The hash key
     * @param cursor The cursor for pagination ("0" to start)
     * @return CompletableFuture that completes with HScanResult containing next cursor and field-value pairs
     */
    public CompletableFuture<HScanResult> hScanAsync(String key, String cursor) {
        return CompletableFuture.supplyAsync(() -> hScan(key, cursor));
    }

    /**
     * Get the number of fields in a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with the number of fields in the hash, or 0 if key doesn't exist or all nodes are unavailable
     */
    public CompletableFuture<Long> hLenAsync(String key) {
        return CompletableFuture.supplyAsync(() -> hLen(key));
    }

    /**
     * Add one or more members to a set asynchronously.
     *
     * @param key     The set key
     * @param members The members to add
     * @return CompletableFuture that completes with the number of members that were added (not already in set)
     */
    public CompletableFuture<Long> sAddAsync(String key, String... members) {
        return CompletableFuture.supplyAsync(() -> sAdd(key, members));
    }

    /**
     * Get all members of a set asynchronously.
     *
     * @param key The set key
     * @return CompletableFuture that completes with a set of all members in the set, or empty set if key doesn't exist or all nodes are unavailable
     */
    public CompletableFuture<Set<String>> sMembersAsync(String key) {
        return CompletableFuture.supplyAsync(() -> sMembers(key));
    }

    /**
     * Delete multiple keys asynchronously.
     *
     * @param keys The keys to delete
     * @return CompletableFuture that completes with the number of keys that were deleted
     */
    public CompletableFuture<Long> delMultipleAsync(String... keys) {
        return CompletableFuture.supplyAsync(() -> delMultiple(keys));
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture<String> scriptLoadAsync(String script) {
        return CompletableFuture.supplyAsync(() -> scriptLoad(script));
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<Object> evalShaAsync(String sha, List<String> keys, List<String> args) {
        return CompletableFuture.supplyAsync(() -> evalSha(sha, keys, args));
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously (no arguments).
     *
     * @param sha The SHA hash of the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<Object> evalShaAsync(String sha) {
        return CompletableFuture.supplyAsync(() -> evalSha(sha));
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously (with keys only).
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<Object> evalShaAsync(String sha, List<String> keys) {
        return CompletableFuture.supplyAsync(() -> evalSha(sha, keys));
    }

    /**
     * Execute a Lua script directly asynchronously (without pre-loading).
     *
     * @param script The Lua script to execute
     * @param keys   List of keys to pass to the script
     * @param args   List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<Object> evalAsync(String script, List<String> keys, List<String> args) {
        return CompletableFuture.supplyAsync(() -> eval(script, keys, args));
    }

    /**
     * Ping the Redis server asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture<Boolean> pingAsync() {
        return CompletableFuture.supplyAsync(this::ping);
    }

    @Override
    public void close() {
        logger.info("Closing RedisClient");
        connectionManager.close();
        logger.info("RedisClient closed");
    }
}
