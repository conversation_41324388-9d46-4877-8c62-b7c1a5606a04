package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionManager;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with RustyCluster.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * <pre>
 * RustyClusterClientConfig config = RustyClusterClientConfig.builder()
 *     .addNode("localhost", 50051, NodeRole.PRIMARY)
 *     .build();
 *
 * try (RustyClusterClient client = new RustyClusterClient(config)) {
 *     // Synchronous operations
 *     client.set("key1", "value1");
 *     String value = client.get("key1");
 *
 *     // Asynchronous operations
 *     CompletableFuture&lt;Boolean&gt; setFuture = client.setAsync("key2", "value2");
 *     CompletableFuture&lt;String&gt; getFuture = client.getAsync("key2");
 * }
 * </pre>
 */
public class RustyClusterClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AsyncConnectionManager asyncConnectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
    public RustyClusterClient(RustyClusterClientConfig config) {
        this.config = config;
        this.authenticationManager = new AuthenticationManager(config);
        this.connectionManager = new ConnectionManager(config, authenticationManager);
        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);
        logger.info("RustyClusterClient initialized with both sync and async capabilities");
    }

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {
        this.config = config;
        this.authenticationManager = new AuthenticationManager(config);
        this.connectionManager = connectionManager;
        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);
        logger.info("RustyClusterClient initialized with custom connection manager");
    }

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
        logger.debug("Setting key: {}", key);

        try {
            rustycluster.Rustycluster.SetRequest request = rustycluster.Rustycluster.SetRequest.newBuilder()
                    .setKey(key)
                    .setValue(value)
                    .setSkipReplication(skipReplication)
                    .setSkipSiteReplication(false)
                    .build();

            rustycluster.Rustycluster.SetResponse response = connectionManager.executeWithFailover(stub ->
                    stub.set(request), OperationType.WRITE);

            return response.getSuccess();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
        return set(key, value, false);
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found or if all nodes are unavailable
     */
    public String get(String key) {
        logger.debug("Getting key: {}", key);
        rustycluster.Rustycluster.GetRequest request = rustycluster.Rustycluster.GetRequest.newBuilder()
                .setKey(key)
                .build();

        rustycluster.Rustycluster.GetResponse response = connectionManager.executeWithFailoverSilent(stub ->
                stub.get(request));

        return response != null && response.getFound() ? response.getValue() : null;
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
        logger.debug("Deleting key: {}", key);
        rustycluster.Rustycluster.DeleteRequest request = rustycluster.Rustycluster.DeleteRequest.newBuilder()
                .setKey(key)
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.DeleteResponse response = connectionManager.executeWithFailover(stub ->
                stub.delete(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
        return delete(key, false);
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
        logger.debug("Setting key with expiry: {}, ttl: {}", key, ttl);
        rustycluster.Rustycluster.SetExRequest request = Rustycluster.SetExRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setTtl(ttl)
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.SetExResponse response = connectionManager.executeWithFailover(stub ->
                stub.setEx(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
        return setEx(key, value, ttl, false);
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
        logger.debug("Setting expiry on key: {}, ttl: {}", key, ttl);
        rustycluster.Rustycluster.SetExpiryRequest request = rustycluster.Rustycluster.SetExpiryRequest.newBuilder()
                .setKey(key)
                .setTtl(ttl)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.SetExpiryResponse response = connectionManager.executeWithFailover(stub ->
                stub.setExpiry(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
        return setExpiry(key, ttl, false);
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
        logger.debug("Incrementing key: {} by {}", key, value);
        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.IncrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.incrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
        return incrBy(key, value, false);
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
        logger.debug("Decrementing key: {} by {}", key, value);
        rustycluster.Rustycluster.DecrByRequest request = rustycluster.Rustycluster.DecrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.DecrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.decrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
        return decrBy(key, value, false);
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
        logger.debug("Incrementing key: {} by float {}", key, value);
        rustycluster.Rustycluster.IncrByFloatRequest request = rustycluster.Rustycluster.IncrByFloatRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.IncrByFloatResponse response = connectionManager.executeWithFailover(stub ->
                stub.incrByFloat(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
        return incrByFloat(key, value, false);
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
        logger.debug("Setting hash field: {}.{}", key, field);
        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.HSetResponse response = connectionManager.executeWithFailover(stub ->
                stub.hSet(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
        return hSet(key, field, value, false);
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found or if all nodes are unavailable
     */
    public String hGet(String key, String field) {
        logger.debug("Getting hash field: {}.{}", key, field);
        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .build();

        rustycluster.Rustycluster.HGetResponse response = connectionManager.executeWithFailoverSilent(stub ->
                stub.hGet(request));

        return response != null && response.getFound() ? response.getValue() : null;
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map<String, String> hGetAll(String key) {
        logger.debug("Getting all hash fields for key: {}", key);
        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()
                .setKey(key)
                .build();

        rustycluster.Rustycluster.HGetAllResponse response = connectionManager.executeWithFailover(stub ->
                stub.hGetAll(request), OperationType.READ);

        return response.getFieldsMap();
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
        logger.debug("Incrementing hash field: {}.{} by {}", key, field, value);
        rustycluster.Rustycluster.HIncrByRequest request = rustycluster.Rustycluster.HIncrByRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.HIncrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.hIncrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
        return hIncrBy(key, field, value, false);
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
        logger.debug("Decrementing hash field: {}.{} by {}", key, field, value);
        rustycluster.Rustycluster.HDecrByRequest request = rustycluster.Rustycluster.HDecrByRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.HDecrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.hDecrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
        return hDecrBy(key, field, value, false);
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
        logger.debug("Incrementing hash field: {}.{} by float {}", key, field, value);
        rustycluster.Rustycluster.HIncrByFloatRequest request = rustycluster.Rustycluster.HIncrByFloatRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub ->
                stub.hIncrByFloat(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
        return hIncrByFloat(key, field, value, false);
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List<Boolean> batchWrite(List<rustycluster.Rustycluster.BatchOperation> operations, boolean skipReplication) {
        logger.debug("Executing batch write with {} operations", operations.size());
        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()
                .addAllOperations(operations)
                .setSkipReplication(skipReplication)
                .build();

        rustycluster.Rustycluster.BatchWriteResponse response = connectionManager.executeWithFailover(stub ->
                stub.batchWrite(request), OperationType.WRITE);

        return response.getOperationResultsList();
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List<Boolean> batchWrite(List<rustycluster.Rustycluster.BatchOperation> operations) {
        return batchWrite(operations, false);
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
        logger.debug("Attempting to authenticate with RustyCluster server");

        if (!config.hasAuthentication()) {
            logger.debug("No authentication credentials configured");
            return true;
        }

        try {
            // Get a stub from the connection manager and authenticate
            return connectionManager.executeWithFailover(stub ->
                authenticationManager.authenticate(stub), OperationType.AUTH);
        } catch (Exception e) {
            logger.error("Authentication failed", e);
            return false;
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
        return authenticationManager.isAuthenticated();
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
        return authenticationManager.getSessionToken();
    }

    // ==================== NEW METHODS ====================

    /**
     * Set multiple fields in a hash.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map<String, String> fields, boolean skipReplication) {
        logger.debug("Setting multiple hash fields for key: {}, fields count: {}", key, fields.size());

        try {
            rustycluster.Rustycluster.HMSetRequest request = rustycluster.Rustycluster.HMSetRequest.newBuilder()
                    .setKey(key)
                    .putAllFields(fields)
                    .setSkipReplication(skipReplication)
                    .setSkipSiteReplication(false)
                    .build();

            rustycluster.Rustycluster.HMSetResponse response = connectionManager.executeWithFailover(
                    stub -> stub.hMSet(request), OperationType.WRITE
            );

            return response.getSuccess();
        } catch (Exception e) {
            logger.error("Error setting multiple hash fields for key: {}", key, e);
            return false;
        }
    }

    /**
     * Set multiple fields in a hash with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map<String, String> fields) {
        return hMSet(key, fields, false);
    }

    /**
     * Check if a key exists.
     *
     * @param key The key to check
     * @return True if the key exists, false otherwise or if all nodes are unavailable
     */
    public boolean exists(String key) {
        logger.debug("Checking if key exists: {}", key);

        rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()
                .setKey(key)
                .build();

        rustycluster.Rustycluster.ExistsResponse response = connectionManager.executeWithFailoverSilent(
                stub -> stub.exists(request)
        );

        return response != null && response.getExists();
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise or if all nodes are unavailable
     */
    public boolean hExists(String key, String field) {
        logger.debug("Checking if hash field exists: {}.{}", key, field);

        rustycluster.Rustycluster.HExistsRequest request = rustycluster.Rustycluster.HExistsRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .build();

        rustycluster.Rustycluster.HExistsResponse response = connectionManager.executeWithFailoverSilent(
                stub -> stub.hExists(request)
        );

        return response != null && response.getExists();
    }

    /**
     * Delete one or more fields from a hash.
     *
     * @param key             The hash key
     * @param fields          The field names to delete
     * @param skipReplication Whether to skip replication
     * @return The number of fields that were deleted
     */
    public int hDel(String key, String[] fields, boolean skipReplication) {
        logger.debug("Deleting hash fields: {} from key: {}", String.join(", ", fields), key);

        rustycluster.Rustycluster.HDelRequest request = rustycluster.Rustycluster.HDelRequest.newBuilder()
                .setKey(key)
                .addAllFields(List.of(fields))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.HDelResponse response = connectionManager.executeWithFailover(stub ->
                stub.hDel(request), OperationType.WRITE);

        return response.getDeletedCount();
    }

    /**
     * Delete one or more fields from a hash with default replication.
     *
     * @param key    The hash key
     * @param fields The field names to delete
     * @return The number of fields that were deleted
     */
    public int hDel(String key, String... fields) {
        return hDel(key, fields, false);
    }

    /**
     * Scan hash fields with optional pattern matching.
     *
     * @param key     The hash key
     * @param cursor  The cursor for pagination (0 to start)
     * @param pattern Optional pattern to match field names (can be null)
     * @param count   Optional hint for number of elements to return (can be null)
     * @return HScanResult containing next cursor and field-value pairs
     */
    public HScanResult hScan(String key, long cursor, String pattern, Integer count) {
        logger.debug("Scanning hash fields for key: {}, cursor: {}, pattern: {}, count: {}",
                     key, cursor, pattern, count);

        rustycluster.Rustycluster.HScanRequest.Builder requestBuilder = rustycluster.Rustycluster.HScanRequest.newBuilder()
                .setKey(key)
                .setCursor(cursor);

        if (pattern != null) {
            requestBuilder.setPattern(pattern);
        }
        if (count != null) {
            requestBuilder.setCount(count);
        }

        rustycluster.Rustycluster.HScanRequest request = requestBuilder.build();

        rustycluster.Rustycluster.HScanResponse response = connectionManager.executeWithFailoverSilent(stub ->
                stub.hScan(request));

        if (response == null) {
            return new HScanResult(0, Map.of());
        }

        return new HScanResult(response.getNextCursor(), response.getFieldsMap());
    }

    /**
     * Scan hash fields with default parameters.
     *
     * @param key    The hash key
     * @param cursor The cursor for pagination (0 to start)
     * @return HScanResult containing next cursor and field-value pairs
     */
    public HScanResult hScan(String key, long cursor) {
        return hScan(key, cursor, null, null);
    }

    /**
     * Get the number of fields in a hash.
     *
     * @param key The hash key
     * @return The number of fields in the hash, or 0 if key doesn't exist or all nodes are unavailable
     */
    public int hLen(String key) {
        logger.debug("Getting hash length for key: {}", key);

        rustycluster.Rustycluster.HLenRequest request = rustycluster.Rustycluster.HLenRequest.newBuilder()
                .setKey(key)
                .build();

        rustycluster.Rustycluster.HLenResponse response = connectionManager.executeWithFailoverSilent(stub ->
                stub.hLen(request));

        return response != null ? response.getLength() : 0;
    }

    /**
     * Add one or more members to a set.
     *
     * @param key             The set key
     * @param members         The members to add
     * @param skipReplication Whether to skip replication
     * @return The number of members that were added (not already in set)
     */
    public int sAdd(String key, String[] members, boolean skipReplication) {
        logger.debug("Adding members to set: {} for key: {}", String.join(", ", members), key);

        rustycluster.Rustycluster.SAddRequest request = rustycluster.Rustycluster.SAddRequest.newBuilder()
                .setKey(key)
                .addAllMembers(List.of(members))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.SAddResponse response = connectionManager.executeWithFailover(stub ->
                stub.sAdd(request), OperationType.WRITE);

        return response.getAddedCount();
    }

    /**
     * Add one or more members to a set with default replication.
     *
     * @param key     The set key
     * @param members The members to add
     * @return The number of members that were added (not already in set)
     */
    public int sAdd(String key, String... members) {
        return sAdd(key, members, false);
    }

    /**
     * Get all members of a set.
     *
     * @param key The set key
     * @return List of all members in the set, or empty list if key doesn't exist or all nodes are unavailable
     */
    public List<String> sMembers(String key) {
        logger.debug("Getting all members of set for key: {}", key);

        rustycluster.Rustycluster.SMembersRequest request = rustycluster.Rustycluster.SMembersRequest.newBuilder()
                .setKey(key)
                .build();

        rustycluster.Rustycluster.SMembersResponse response = connectionManager.executeWithFailoverSilent(stub ->
                stub.sMembers(request));

        return response != null ? response.getMembersList() : List.of();
    }

    /**
     * Delete multiple keys.
     *
     * @param keys            The keys to delete
     * @param skipReplication Whether to skip replication
     * @return The number of keys that were deleted
     */
    public int delMultiple(String[] keys, boolean skipReplication) {
        logger.debug("Deleting multiple keys: {}", String.join(", ", keys));

        rustycluster.Rustycluster.DelMultipleRequest request = rustycluster.Rustycluster.DelMultipleRequest.newBuilder()
                .addAllKeys(List.of(keys))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        rustycluster.Rustycluster.DelMultipleResponse response = connectionManager.executeWithFailover(stub ->
                stub.delMultiple(request), OperationType.WRITE);

        return response.getDeletedCount();
    }

    /**
     * Delete multiple keys with default replication.
     *
     * @param keys The keys to delete
     * @return The number of keys that were deleted
     */
    public int delMultiple(String... keys) {
        return delMultiple(keys, false);
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value, boolean skipReplication) {
        logger.debug("Setting key if not exists: {}", key);

        try {
            rustycluster.Rustycluster.SetNXRequest request = rustycluster.Rustycluster.SetNXRequest.newBuilder()
                    .setKey(key)
                    .setValue(value)
                    .setSkipReplication(skipReplication)
                    .setSkipSiteReplication(false)
                    .build();

            rustycluster.Rustycluster.SetNXResponse response = connectionManager.executeWithFailover(
                    stub -> stub.setNX(request), OperationType.WRITE
            );

            return response.getSuccess();
        } catch (Exception e) {
            logger.error("Error setting key if not exists: {}", key, e);
            return false;
        }
    }

    /**
     * Set a key-value pair only if the key does not exist with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
        return setNX(key, value, false);
    }



    /**
     * Load a Lua script and return its SHA hash.
     *
     * @param script The Lua script to load
     * @return The SHA hash of the loaded script, or null if failed
     */
    public String loadScript(String script) {
        logger.debug("Loading script, length: {}", script.length());

        try {
            rustycluster.Rustycluster.LoadScriptRequest request = rustycluster.Rustycluster.LoadScriptRequest.newBuilder()
                    .setScript(script)
                    .build();

            rustycluster.Rustycluster.LoadScriptResponse response = connectionManager.executeWithFailover(
                    stub -> stub.loadScript(request), OperationType.WRITE
            );

            return response.getSuccess() ? response.getSha() : null;
        } catch (Exception e) {
            logger.error("Error loading script", e);
            return null;
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List<String> keys, List<String> args, boolean skipReplication) {
        logger.debug("Executing script with SHA: {}, keys: {}, args: {}", sha, keys.size(), args.size());

        try {
            rustycluster.Rustycluster.EvalShaRequest request = rustycluster.Rustycluster.EvalShaRequest.newBuilder()
                    .setSha(sha)
                    .addAllKeys(keys)
                    .addAllArgs(args)
                    .setSkipReplication(skipReplication)
                    .setSkipSiteReplication(false)
                    .build();

            rustycluster.Rustycluster.EvalShaResponse response = connectionManager.executeWithFailover(
                    stub -> stub.evalSha(request), OperationType.WRITE
            );

            return response.getSuccess() ? response.getResult() : null;
        } catch (Exception e) {
            logger.error("Error executing script with SHA: {}", sha, e);
            return null;
        }
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List<String> keys, List<String> args) {
        return evalSha(sha, keys, args, false);
    }

    /**
     * Perform a health check on the cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
        logger.debug("Performing health check");

        // Use ping for now until HealthCheck RPC is available
        try {
            return ping();
        } catch (Exception e) {
            logger.warn("Health check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Ping the cluster to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
        logger.debug("Pinging cluster");

        try {
            rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();
            rustycluster.Rustycluster.PingResponse response = connectionManager.executeWithFailover(stub ->
                    stub.ping(request), OperationType.READ);
            return response.getSuccess();
        } catch (Exception e) {
            logger.warn("Ping failed: {}", e.getMessage());
            return false;
        }
    }

    // ==================== ASYNCHRONOUS METHODS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value, boolean skipReplication) {
        logger.debug("Setting key asynchronously: {}", key);
        rustycluster.Rustycluster.SetRequest request = rustycluster.Rustycluster.SetRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.set(request))
                .thenApply(rustycluster.Rustycluster.SetResponse::getSuccess);
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> setAsync(String key, String value) {
        return setAsync(key, value, false);
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found or if all nodes are unavailable
     */
    public CompletableFuture<String> getAsync(String key) {
        logger.debug("Getting key asynchronously: {}", key);
        rustycluster.Rustycluster.GetRequest request = rustycluster.Rustycluster.GetRequest.newBuilder()
                .setKey(key)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.get(request))
                .thenApply(response -> response != null && response.getFound() ? response.getValue() : null);
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key, boolean skipReplication) {
        logger.debug("Deleting key asynchronously: {}", key);
        rustycluster.Rustycluster.DeleteRequest request = rustycluster.Rustycluster.DeleteRequest.newBuilder()
                .setKey(key)
                .setSkipReplication(skipReplication)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.delete(request))
                .thenApply(rustycluster.Rustycluster.DeleteResponse::getSuccess);
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> deleteAsync(String key) {
        return deleteAsync(key, false);
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture<List<Boolean>> batchWriteAsync(List<rustycluster.Rustycluster.BatchOperation> operations, boolean skipReplication) {
        logger.debug("Executing batch write asynchronously with {} operations", operations.size());
        rustycluster.Rustycluster.BatchWriteRequest request = rustycluster.Rustycluster.BatchWriteRequest.newBuilder()
                .addAllOperations(operations)
                .setSkipReplication(skipReplication)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.batchWrite(request))
                .thenApply(rustycluster.Rustycluster.BatchWriteResponse::getOperationResultsList);
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture<List<Boolean>> batchWriteAsync(List<rustycluster.Rustycluster.BatchOperation> operations) {
        return batchWriteAsync(operations, false);
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture<Long> incrByAsync(String key, long value, boolean skipReplication) {
        logger.debug("Incrementing key asynchronously: {} by {}", key, value);
        rustycluster.Rustycluster.IncrByRequest request = rustycluster.Rustycluster.IncrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.incrBy(request))
                .thenApply(rustycluster.Rustycluster.IncrByResponse::getNewValue);
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture<Long> incrByAsync(String key, long value) {
        return incrByAsync(key, value, false);
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hSetAsync(String key, String field, String value, boolean skipReplication) {
        logger.debug("Setting hash field asynchronously: {}.{}", key, field);
        rustycluster.Rustycluster.HSetRequest request = rustycluster.Rustycluster.HSetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.hSet(request))
                .thenApply(rustycluster.Rustycluster.HSetResponse::getSuccess);
    }

    /**
     * Set a hash field asynchronously with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hSetAsync(String key, String field, String value) {
        return hSetAsync(key, field, value, false);
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found or if all nodes are unavailable
     */
    public CompletableFuture<String> hGetAsync(String key, String field) {
        logger.debug("Getting hash field asynchronously: {}.{}", key, field);
        rustycluster.Rustycluster.HGetRequest request = rustycluster.Rustycluster.HGetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.hGet(request))
                .thenApply(response -> response != null && response.getFound() ? response.getValue() : null);
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture<Map<String, String>> hGetAllAsync(String key) {
        logger.debug("Getting all hash fields asynchronously for key: {}", key);
        rustycluster.Rustycluster.HGetAllRequest request = rustycluster.Rustycluster.HGetAllRequest.newBuilder()
                .setKey(key)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.hGetAll(request))
                .thenApply(rustycluster.Rustycluster.HGetAllResponse::getFieldsMap);
    }

    // ==================== NEW ASYNC METHODS ====================

    /**
     * Set multiple fields in a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hMSetAsync(String key, Map<String, String> fields, boolean skipReplication) {
        logger.debug("Setting multiple hash fields asynchronously for key: {}, fields count: {}", key, fields.size());

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
        List<CompletableFuture<Boolean>> futures = fields.entrySet().stream()
                .map(entry -> hSetAsync(key, entry.getKey(), entry.getValue(), skipReplication))
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().allMatch(CompletableFuture::join));
    }

    /**
     * Set multiple fields in a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture<Boolean> hMSetAsync(String key, Map<String, String> fields) {
        return hMSetAsync(key, fields, false);
    }

    /**
     * Check if a hash field exists asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with true if field exists, false otherwise
     */
    public CompletableFuture<Boolean> hExistsAsync(String key, String field) {
        logger.debug("Checking if hash field exists asynchronously: {}.{}", key, field);

        rustycluster.Rustycluster.HExistsRequest request = rustycluster.Rustycluster.HExistsRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.hExists(request))
                .thenApply(response -> response != null && response.getExists());
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if key exists, false otherwise or if all nodes are unavailable
     */
    public CompletableFuture<Boolean> existsAsync(String key) {
        logger.debug("Checking if key exists asynchronously: {}", key);

        rustycluster.Rustycluster.ExistsRequest request = rustycluster.Rustycluster.ExistsRequest.newBuilder()
                .setKey(key)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.exists(request))
                .thenApply(response -> response != null && response.getExists());
    }

    /**
     * Delete one or more fields from a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          The field names to delete
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the number of fields that were deleted
     */
    public CompletableFuture<Integer> hDelAsync(String key, String[] fields, boolean skipReplication) {
        logger.debug("Deleting hash fields asynchronously: {} from key: {}", String.join(", ", fields), key);

        rustycluster.Rustycluster.HDelRequest request = rustycluster.Rustycluster.HDelRequest.newBuilder()
                .setKey(key)
                .addAllFields(List.of(fields))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.hDel(request))
                .thenApply(rustycluster.Rustycluster.HDelResponse::getDeletedCount);
    }

    /**
     * Delete one or more fields from a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields The field names to delete
     * @return CompletableFuture that completes with the number of fields that were deleted
     */
    public CompletableFuture<Integer> hDelAsync(String key, String... fields) {
        return hDelAsync(key, fields, false);
    }

    /**
     * Scan hash fields with optional pattern matching asynchronously.
     *
     * @param key     The hash key
     * @param cursor  The cursor for pagination (0 to start)
     * @param pattern Optional pattern to match field names (can be null)
     * @param count   Optional hint for number of elements to return (can be null)
     * @return CompletableFuture that completes with HScanResult containing next cursor and field-value pairs
     */
    public CompletableFuture<HScanResult> hScanAsync(String key, long cursor, String pattern, Integer count) {
        logger.debug("Scanning hash fields asynchronously for key: {}, cursor: {}, pattern: {}, count: {}",
                     key, cursor, pattern, count);

        rustycluster.Rustycluster.HScanRequest.Builder requestBuilder = rustycluster.Rustycluster.HScanRequest.newBuilder()
                .setKey(key)
                .setCursor(cursor);

        if (pattern != null) {
            requestBuilder.setPattern(pattern);
        }
        if (count != null) {
            requestBuilder.setCount(count);
        }

        rustycluster.Rustycluster.HScanRequest request = requestBuilder.build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.hScan(request))
                .thenApply(response -> {
                    if (response == null) {
                        return new HScanResult(0, Map.of());
                    }
                    return new HScanResult(response.getNextCursor(), response.getFieldsMap());
                });
    }

    /**
     * Scan hash fields with default parameters asynchronously.
     *
     * @param key    The hash key
     * @param cursor The cursor for pagination (0 to start)
     * @return CompletableFuture that completes with HScanResult containing next cursor and field-value pairs
     */
    public CompletableFuture<HScanResult> hScanAsync(String key, long cursor) {
        return hScanAsync(key, cursor, null, null);
    }

    /**
     * Get the number of fields in a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with the number of fields in the hash, or 0 if key doesn't exist or all nodes are unavailable
     */
    public CompletableFuture<Integer> hLenAsync(String key) {
        logger.debug("Getting hash length asynchronously for key: {}", key);

        rustycluster.Rustycluster.HLenRequest request = rustycluster.Rustycluster.HLenRequest.newBuilder()
                .setKey(key)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.hLen(request))
                .thenApply(response -> response != null ? response.getLength() : 0);
    }

    /**
     * Add one or more members to a set asynchronously.
     *
     * @param key             The set key
     * @param members         The members to add
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the number of members that were added (not already in set)
     */
    public CompletableFuture<Integer> sAddAsync(String key, String[] members, boolean skipReplication) {
        logger.debug("Adding members to set asynchronously: {} for key: {}", String.join(", ", members), key);

        rustycluster.Rustycluster.SAddRequest request = rustycluster.Rustycluster.SAddRequest.newBuilder()
                .setKey(key)
                .addAllMembers(List.of(members))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.sAdd(request))
                .thenApply(rustycluster.Rustycluster.SAddResponse::getAddedCount);
    }

    /**
     * Add one or more members to a set asynchronously with default replication.
     *
     * @param key     The set key
     * @param members The members to add
     * @return CompletableFuture that completes with the number of members that were added (not already in set)
     */
    public CompletableFuture<Integer> sAddAsync(String key, String... members) {
        return sAddAsync(key, members, false);
    }

    /**
     * Get all members of a set asynchronously.
     *
     * @param key The set key
     * @return CompletableFuture that completes with a list of all members in the set, or empty list if key doesn't exist or all nodes are unavailable
     */
    public CompletableFuture<List<String>> sMembersAsync(String key) {
        logger.debug("Getting all members of set asynchronously for key: {}", key);

        rustycluster.Rustycluster.SMembersRequest request = rustycluster.Rustycluster.SMembersRequest.newBuilder()
                .setKey(key)
                .build();

        return asyncConnectionManager.executeWithFailoverAsyncSilent(stub ->
                stub.sMembers(request))
                .thenApply(response -> response != null ? response.getMembersList() : List.of());
    }

    /**
     * Delete multiple keys asynchronously.
     *
     * @param keys            The keys to delete
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the number of keys that were deleted
     */
    public CompletableFuture<Integer> delMultipleAsync(String[] keys, boolean skipReplication) {
        logger.debug("Deleting multiple keys asynchronously: {}", String.join(", ", keys));

        rustycluster.Rustycluster.DelMultipleRequest request = rustycluster.Rustycluster.DelMultipleRequest.newBuilder()
                .addAllKeys(List.of(keys))
                .setSkipReplication(skipReplication)
                .setSkipSiteReplication(false)
                .build();

        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.delMultiple(request))
                .thenApply(rustycluster.Rustycluster.DelMultipleResponse::getDeletedCount);
    }

    /**
     * Delete multiple keys asynchronously with default replication.
     *
     * @param keys The keys to delete
     * @return CompletableFuture that completes with the number of keys that were deleted
     */
    public CompletableFuture<Integer> delMultipleAsync(String... keys) {
        return delMultipleAsync(keys, false);
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture<Boolean> setNXAsync(String key, String value, boolean skipReplication) {
        logger.debug("Setting key if not exists asynchronously: {}", key);

        // Temporary implementation using exists check + set until gRPC classes are regenerated
        return existsAsync(key).thenCompose(exists -> {
            if (exists) {
                return CompletableFuture.completedFuture(false); // Key already exists
            }
            return setAsync(key, value, skipReplication);
        });
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture<Boolean> setNXAsync(String key, String value) {
        return setNXAsync(key, value, false);
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture<String> loadScriptAsync(String script) {
        logger.debug("Loading script asynchronously, length: {}", script.length());

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
        logger.warn("loadScriptAsync is not fully implemented yet - requires gRPC class regeneration");
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<String> evalShaAsync(String sha, List<String> keys, List<String> args, boolean skipReplication) {
        logger.debug("Executing script asynchronously with SHA: {}, keys: {}, args: {}", sha, keys.size(), args.size());

        // Temporary implementation until gRPC classes are regenerated
        logger.warn("evalShaAsync is not fully implemented yet - requires gRPC class regeneration");
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture<String> evalShaAsync(String sha, List<String> keys, List<String> args) {
        return evalShaAsync(sha, keys, args, false);
    }

    /**
     * Perform a health check on the cluster asynchronously.
     *
     * @return CompletableFuture that completes with true if healthy, false otherwise
     */
    public CompletableFuture<Boolean> healthCheckAsync() {
        logger.debug("Performing health check asynchronously");

        // Use ping for now until HealthCheck RPC is available
        return pingAsync().exceptionally(throwable -> {
            logger.warn("Health check failed: {}", throwable.getMessage());
            return false;
        });
    }

    /**
     * Ping the cluster to check connectivity asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture<Boolean> pingAsync() {
        logger.debug("Pinging cluster asynchronously");

        rustycluster.Rustycluster.PingRequest request = rustycluster.Rustycluster.PingRequest.newBuilder().build();
        return asyncConnectionManager.executeWithFailoverAsync(stub ->
                stub.ping(request))
                .thenApply(rustycluster.Rustycluster.PingResponse::getSuccess)
                .exceptionally(throwable -> {
                    logger.warn("Ping failed: {}", throwable.getMessage());
                    return false;
                });
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture<Boolean> authenticateAsync() {
        logger.debug("Attempting to authenticate asynchronously with RustyCluster server");

        if (!config.hasAuthentication()) {
            logger.debug("No authentication credentials configured");
            return CompletableFuture.completedFuture(true);
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
        return CompletableFuture.supplyAsync(() -> {
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
                return authenticationManager.isAuthenticated();
            } catch (Exception e) {
                logger.error("Authentication check failed", e);
                return false;
            }
        });
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
        authenticationManager.clearAuthentication();
        connectionManager.close();
        asyncConnectionManager.close();
        logger.info("RustyClusterClient closed");
    }
}
