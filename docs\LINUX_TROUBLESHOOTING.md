# Linux Troubleshooting Guide

This guide addresses common issues when running the RustyCluster Java client on Linux systems, particularly the `UnsupportedAddressTypeException`.

## Common Issue: UnsupportedAddressTypeException

### Symptoms
```
java.nio.channels.UnsupportedAddressTypeException
    at sun.nio.ch.Net.checkAddress(Net.java:146)
    at sun.nio.ch.Net.checkAddress(Net.java:157)
    at sun.nio.ch.SocketChannelImpl.checkRemote(SocketChannelImpl.java:816)
    at sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:839)
```

### Root Cause
This exception typically occurs when:
1. IPv6 addresses are resolved but the system doesn't properly support them
2. Hostname resolution returns IPv6 addresses on Linux but IPv4 on Windows
3. The JVM's network stack configuration differs between platforms

## Solutions

### CRITICAL: Apply ALL Three Solutions Together

The `UnsupportedAddressTypeException` requires a multi-layered approach. You MUST apply all three solutions:

### 1. JVM Properties (Apply FIRST)
Add these JVM arguments when starting your application:
```bash
java -Djava.net.preferIPv4Stack=true \
     -Djava.net.preferIPv6Addresses=false \
     -Dnetworkaddress.cache.ttl=30 \
     -Dnetworkaddress.cache.negative.ttl=10 \
     your.application.Main
```

### 2. Programmatic Configuration (Apply BEFORE creating client)
```java
import org.npci.rustyclient.client.util.NetworkCompatibilityUtils;

public class YourApplication {
    public static void main(String[] args) {
        // CRITICAL: This MUST be the first line in your main method
        NetworkCompatibilityUtils.configureLinuxNetworking();

        // If the above doesn't work, try force configuration:
        // NetworkCompatibilityUtils.forceConfigureNetworking();

        // Now create your client...
        RustyClusterClientConfig config = ...
    }
}
```

**Alternative: Static Initialization (Recommended)**
```java
public class YourApplication {
    static {
        // This runs before main() - ensures early configuration
        NetworkCompatibilityUtils.configureLinuxNetworking();
    }

    public static void main(String[] args) {
        // Your application code
    }
}
```

### 3. Client Configuration - Use IP Addresses + preferIpv4
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .addPrimaryNode("127.0.0.1", 50051)    // NEVER use "localhost"
    .addSecondaryNode("*************", 50051)  // Use actual IP addresses
    .preferIpv4(true)  // CRITICAL: Enable IPv4 preference
    .build();
```

### 4. If Still Failing - Emergency Fix
If you're still getting the exception, try this emergency configuration:
```java
// Emergency configuration - apply before anything else
System.setProperty("java.net.preferIPv4Stack", "true");
System.setProperty("java.net.preferIPv6Addresses", "false");
NetworkCompatibilityUtils.forceConfigureNetworking();
```

### 4. System-Level Fixes

#### Check /etc/hosts
Ensure your `/etc/hosts` file has proper IPv4 entries:
```bash
# /etc/hosts
127.0.0.1   localhost
*********   your-hostname
```

#### Disable IPv6 (if not needed)
```bash
# Temporarily disable IPv6
sudo sysctl -w net.ipv6.conf.all.disable_ipv6=1
sudo sysctl -w net.ipv6.conf.default.disable_ipv6=1

# Permanently disable IPv6
echo 'net.ipv6.conf.all.disable_ipv6 = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.default.disable_ipv6 = 1' | sudo tee -a /etc/sysctl.conf
```

#### Check Network Interface Configuration
```bash
# Check available network interfaces
ip addr show

# Check DNS resolution
nslookup localhost
dig localhost
```

## Best Practices for Linux Deployment

### 1. Use IP Addresses When Possible
```java
// Preferred - Direct IP addresses
.addPrimaryNode("*************", 50051)
.addSecondaryNode("*************", 50051)

// Avoid - Hostnames that might resolve to IPv6
.addPrimaryNode("server1.example.com", 50051)
```

### 2. Configure Timeouts Appropriately
```java
RustyClusterClientConfig config = RustyClusterClientConfig.builder()
    .connectionTimeout(3, TimeUnit.SECONDS)  // Shorter timeout for faster failure detection
    .readTimeout(2, TimeUnit.SECONDS)
    .writeTimeout(2, TimeUnit.SECONDS)
    .maxRetries(2)  // Fewer retries for faster failover
    .build();
```

### 3. Enable Comprehensive Logging
```java
// Add to your logback.xml or log4j2.xml
<logger name="org.npci.rustyclient" level="DEBUG"/>
<logger name="io.grpc" level="DEBUG"/>
```

### 4. Monitor Network Configuration
```java
// Log network properties for debugging
NetworkCompatibilityUtils.logNetworkProperties();

// Validate configuration
if (!NetworkCompatibilityUtils.validateNetworkConfiguration()) {
    logger.warn("Network configuration may cause issues");
}
```

## Debugging Steps

### 1. Check Java Network Properties
```java
System.out.println("java.net.preferIPv4Stack: " + System.getProperty("java.net.preferIPv4Stack"));
System.out.println("java.net.preferIPv6Addresses: " + System.getProperty("java.net.preferIPv6Addresses"));
```

### 2. Test Address Resolution
```bash
# Test hostname resolution
getent hosts localhost
getent hosts your-server-name

# Test network connectivity
telnet your-server-ip 50051
nc -zv your-server-ip 50051
```

### 3. Check gRPC Server Configuration
Ensure your gRPC server is configured to listen on the correct interface:
```bash
# Check what the server is listening on
netstat -tlnp | grep 50051
ss -tlnp | grep 50051
```

## Environment-Specific Solutions

### Docker Containers
```dockerfile
# In your Dockerfile
ENV JAVA_OPTS="-Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false"
```

### Kubernetes
```yaml
# In your deployment.yaml
env:
- name: JAVA_OPTS
  value: "-Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false"
```

### Systemd Services
```ini
# In your service file
[Service]
Environment="JAVA_OPTS=-Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false"
```

## Testing Your Configuration

Use the provided `LinuxCompatibilityExample.java` to test your configuration:

```bash
javac -cp "lib/*" examples/LinuxCompatibilityExample.java
java -cp "lib/*:examples" LinuxCompatibilityExample
```

## Getting Help

If you continue to experience issues:

1. Enable debug logging for network operations
2. Capture the full stack trace
3. Check your system's network configuration
4. Test with the provided compatibility utilities
5. Consider using IP addresses instead of hostnames

For additional support, include the following information:
- Linux distribution and version
- Java version
- Network configuration (`ip addr`, `/etc/hosts`)
- Full stack trace
- Client configuration used
