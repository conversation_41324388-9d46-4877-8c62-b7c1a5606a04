package org.npci.rustyclient.client.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests for NetworkCompatibilityUtils.
 */
class NetworkCompatibilityUtilsTest {

    private String originalPreferIPv4Stack;
    private String originalPreferIPv6Addresses;
    private String originalCacheTtl;
    private String originalNegativeCacheTtl;

    @BeforeEach
    void setUp() {
        // Save original system properties
        originalPreferIPv4Stack = System.getProperty("java.net.preferIPv4Stack");
        originalPreferIPv6Addresses = System.getProperty("java.net.preferIPv6Addresses");
        originalCacheTtl = System.getProperty("networkaddress.cache.ttl");
        originalNegativeCacheTtl = System.getProperty("networkaddress.cache.negative.ttl");
    }

    @AfterEach
    void tearDown() {
        // Restore original system properties
        restoreProperty("java.net.preferIPv4Stack", originalPreferIPv4Stack);
        restoreProperty("java.net.preferIPv6Addresses", originalPreferIPv6Addresses);
        restoreProperty("networkaddress.cache.ttl", originalCacheTtl);
        restoreProperty("networkaddress.cache.negative.ttl", originalNegativeCacheTtl);
    }

    private void restoreProperty(String key, String originalValue) {
        if (originalValue == null) {
            System.clearProperty(key);
        } else {
            System.setProperty(key, originalValue);
        }
    }

    @Test
    @DisplayName("Should configure Linux networking properties")
    void shouldConfigureLinuxNetworkingProperties() {
        // When
        NetworkCompatibilityUtils.configureLinuxNetworking();

        // Then - verify properties are set correctly
        if (NetworkCompatibilityUtils.isLinux()) {
            assertThat(System.getProperty("java.net.preferIPv4Stack")).isEqualTo("true");
            assertThat(System.getProperty("java.net.preferIPv6Addresses")).isEqualTo("false");
            assertThat(System.getProperty("networkaddress.cache.ttl")).isEqualTo("30");
            assertThat(System.getProperty("networkaddress.cache.negative.ttl")).isEqualTo("10");
            assertThat(System.getProperty("java.net.useSystemProxies")).isEqualTo("false");
        }
    }

    @Test
    @DisplayName("Should detect platform correctly")
    void shouldDetectPlatformCorrectly() {
        // Given
        String osName = System.getProperty("os.name", "").toLowerCase();

        // Then
        if (osName.contains("linux")) {
            assertThat(NetworkCompatibilityUtils.isLinux()).isTrue();
            assertThat(NetworkCompatibilityUtils.isWindows()).isFalse();
            assertThat(NetworkCompatibilityUtils.isMac()).isFalse();
        } else if (osName.contains("windows")) {
            assertThat(NetworkCompatibilityUtils.isLinux()).isFalse();
            assertThat(NetworkCompatibilityUtils.isWindows()).isTrue();
            assertThat(NetworkCompatibilityUtils.isMac()).isFalse();
        } else if (osName.contains("mac")) {
            assertThat(NetworkCompatibilityUtils.isLinux()).isFalse();
            assertThat(NetworkCompatibilityUtils.isWindows()).isFalse();
            assertThat(NetworkCompatibilityUtils.isMac()).isTrue();
        }
    }

    @Test
    @DisplayName("Should provide platform-specific recommendations")
    void shouldProvidePlatformSpecificRecommendations() {
        // When
        String recommendations = NetworkCompatibilityUtils.getPlatformRecommendations();

        // Then
        assertThat(recommendations).isNotNull();
        assertThat(recommendations).isNotEmpty();
        assertThat(recommendations).contains("Platform:");

        if (NetworkCompatibilityUtils.isLinux()) {
            assertThat(recommendations).contains("Linux-specific recommendations");
            assertThat(recommendations).contains("configureLinuxNetworking()");
            assertThat(recommendations).contains("preferIpv4(true)");
        } else if (NetworkCompatibilityUtils.isWindows()) {
            assertThat(recommendations).contains("Windows-specific recommendations");
        } else if (NetworkCompatibilityUtils.isMac()) {
            assertThat(recommendations).contains("macOS-specific recommendations");
        }
    }

    @Test
    @DisplayName("Should validate network configuration on Linux")
    void shouldValidateNetworkConfigurationOnLinux() {
        // Given - clear properties first
        System.clearProperty("java.net.preferIPv4Stack");
        System.clearProperty("java.net.preferIPv6Addresses");

        if (NetworkCompatibilityUtils.isLinux()) {
            // When - without proper configuration
            boolean isValidBefore = NetworkCompatibilityUtils.validateNetworkConfiguration();

            // Configure properly
            NetworkCompatibilityUtils.configureLinuxNetworking();

            // When - with proper configuration
            boolean isValidAfter = NetworkCompatibilityUtils.validateNetworkConfiguration();

            // Then
            assertThat(isValidAfter).isTrue();
        } else {
            // On non-Linux platforms, validation should generally pass
            boolean isValid = NetworkCompatibilityUtils.validateNetworkConfiguration();
            assertThat(isValid).isTrue();
        }
    }

    @Test
    @DisplayName("Should not throw exceptions when logging network properties")
    void shouldNotThrowExceptionsWhenLoggingNetworkProperties() {
        // This test ensures the logging method doesn't throw exceptions
        // We can't easily test the actual logging output without complex setup
        
        // When/Then - should not throw
        NetworkCompatibilityUtils.logNetworkProperties();
    }

    @Test
    @DisplayName("Should handle missing system properties gracefully")
    void shouldHandleMissingSystemPropertiesGracefully() {
        // Given - clear all network-related properties
        System.clearProperty("java.net.preferIPv4Stack");
        System.clearProperty("java.net.preferIPv6Addresses");
        System.clearProperty("networkaddress.cache.ttl");
        System.clearProperty("networkaddress.cache.negative.ttl");

        // When/Then - should not throw exceptions
        NetworkCompatibilityUtils.logNetworkProperties();
        NetworkCompatibilityUtils.validateNetworkConfiguration();
        NetworkCompatibilityUtils.getPlatformRecommendations();
    }
}
