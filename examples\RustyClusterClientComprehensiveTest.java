import org.npci.rustyclient.client.RustyClusterClient;
import org.npci.rustyclient.client.HScanResult;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Comprehensive test example demonstrating ALL RustyClusterClient operations.
 * This example shows how to use the RustyClusterClient for all available operations
 * including string operations, hash operations, set operations, script operations,
 * increment/decrement operations, batch operations, authentication, and asynchronous variants.
 */
public class RustyClusterClientComprehensiveTest {

    public static void main(String[] args) {
        System.out.println("=== Comprehensive RustyCluster Client Test Example ===");

        // Configure the client
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addPrimaryNode("localhost", 50051)
                .addSecondaryNode("localhost", 50052)
                .authentication("testuser", "testpass")
                .enableFailback(true)
                .failbackCheckInterval(30, TimeUnit.SECONDS)
                .maxConnectionsPerNode(10)
                .build();

        // Test all operations systematically
        testAuthenticationOperations(config);
        testStringOperations(config);
        testHashOperations(config);
        testSetOperations(config);
        testIncrementDecrementOperations(config);
        testScriptOperations(config);
        testBatchOperations(config);
        testUtilityOperations(config);
        testAsyncOperations(config);

        System.out.println("=== All RustyCluster Client Tests Completed ===");
    }

    /**
     * Test authentication operations
     */
    private static void testAuthenticationOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Authentication Operations ---");
        
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Test authentication
            System.out.println("Testing authenticate operation:");
            boolean authResult = client.authenticate();
            System.out.println("Authentication result: " + authResult);
            
            // Check authentication status
            System.out.println("\nTesting isAuthenticated operation:");
            boolean isAuthResult = client.isAuthenticated();
            System.out.println("Is authenticated: " + isAuthResult);
            
            // Get session token
            System.out.println("\nTesting getSessionToken operation:");
            String sessionToken = client.getSessionToken();
            System.out.println("Session token: " + (sessionToken != null ? "***TOKEN***" : "null"));
            
        } catch (Exception e) {
            System.err.println("Error in authentication operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all string-related operations
     */
    private static void testStringOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing String Operations ---");
        
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Basic set/get operations
            System.out.println("Testing basic set/get operations:");
            boolean setResult = client.set("test:string:key1", "value1");
            System.out.println("Set result: " + setResult);
            
            boolean setWithReplicationResult = client.set("test:string:key2", "value2", false);
            System.out.println("Set with replication result: " + setWithReplicationResult);
            
            String getValue = client.get("test:string:key1");
            System.out.println("Get result: " + getValue);
            
            // Set with expiration
            System.out.println("\nTesting setEx operation:");
            boolean setExResult = client.setEx("test:string:key3", "value3", 300); // 5 minutes
            System.out.println("SetEx result: " + setExResult);
            
            boolean setExWithReplicationResult = client.setEx("test:string:key4", "value4", 300, false);
            System.out.println("SetEx with replication result: " + setExWithReplicationResult);
            
            // Set expiry on existing key
            System.out.println("\nTesting setExpiry operation:");
            boolean setExpiryResult = client.setExpiry("test:string:key1", 600); // 10 minutes
            System.out.println("SetExpiry result: " + setExpiryResult);
            
            boolean setExpiryWithReplicationResult = client.setExpiry("test:string:key2", 600, false);
            System.out.println("SetExpiry with replication result: " + setExpiryWithReplicationResult);
            
            // Set if not exists
            System.out.println("\nTesting setNX operation:");
            boolean setNXResult1 = client.setNX("test:string:key5", "value5");
            System.out.println("SetNX result (new key): " + setNXResult1);
            
            boolean setNXResult2 = client.setNX("test:string:key5", "value5_updated");
            System.out.println("SetNX result (existing key): " + setNXResult2);
            
            boolean setNXWithReplicationResult = client.setNX("test:string:key6", "value6", false);
            System.out.println("SetNX with replication result: " + setNXWithReplicationResult);
            
            // Exists check
            System.out.println("\nTesting exists operation:");
            boolean existsResult = client.exists("test:string:key1");
            System.out.println("Exists result: " + existsResult);
            
            boolean notExistsResult = client.exists("test:string:nonexistent");
            System.out.println("Not exists result: " + notExistsResult);
            
            // Delete operation
            System.out.println("\nTesting delete operation:");
            boolean deleteResult = client.delete("test:string:key3");
            System.out.println("Delete result: " + deleteResult);
            
            boolean deleteWithReplicationResult = client.delete("test:string:key4", false);
            System.out.println("Delete with replication result: " + deleteWithReplicationResult);
            
            // Multiple key deletion
            System.out.println("\nTesting delMultiple operation:");
            client.set("test:string:multi1", "value1");
            client.set("test:string:multi2", "value2");
            client.set("test:string:multi3", "value3");
            
            int delMultipleResult = client.delMultiple("test:string:multi1", "test:string:multi2", "test:string:multi3");
            System.out.println("DelMultiple result: " + delMultipleResult);
            
            // Cleanup remaining keys
            client.delMultiple("test:string:key1", "test:string:key2", "test:string:key5", "test:string:key6");
            
        } catch (Exception e) {
            System.err.println("Error in string operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all hash-related operations
     */
    private static void testHashOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Hash Operations ---");
        
        try (RustyClusterClient client = new RustyClusterClient(config)) {
            String hashKey = "test:hash:user:1001";
            
            // Basic hash set/get
            System.out.println("Testing basic hash operations:");
            boolean hSetResult = client.hSet(hashKey, "name", "John Doe");
            System.out.println("HSet result: " + hSetResult);
            
            boolean hSetWithReplicationResult = client.hSet(hashKey, "email", "<EMAIL>", false);
            System.out.println("HSet with replication result: " + hSetWithReplicationResult);
            
            String hGetResult = client.hGet(hashKey, "name");
            System.out.println("HGet result: " + hGetResult);
            
            // Multiple hash set
            System.out.println("\nTesting hMSet operation:");
            Map<String, String> hashFields = new HashMap<>();
            hashFields.put("age", "30");
            hashFields.put("city", "New York");
            hashFields.put("department", "Engineering");
            
            boolean hMSetResult = client.hMSet(hashKey, hashFields);
            System.out.println("HMSet result: " + hMSetResult);
            
            boolean hMSetWithReplicationResult = client.hMSet(hashKey, 
                Map.of("country", "USA", "timezone", "EST"), false);
            System.out.println("HMSet with replication result: " + hMSetWithReplicationResult);
            
            // Get all hash fields
            System.out.println("\nTesting hGetAll operation:");
            Map<String, String> allFields = client.hGetAll(hashKey);
            System.out.println("HGetAll result: " + allFields);
            
            // Hash field exists
            System.out.println("\nTesting hExists operation:");
            boolean hExistsResult = client.hExists(hashKey, "name");
            System.out.println("HExists result: " + hExistsResult);
            
            // Hash increment operations
            System.out.println("\nTesting hash increment operations:");
            client.hSet(hashKey, "counter", "10");
            client.hSet(hashKey, "score", "85");
            
            long hIncrByResult = client.hIncrBy(hashKey, "counter", 5);
            System.out.println("HIncrBy result: " + hIncrByResult);
            
            long hIncrByWithReplicationResult = client.hIncrBy(hashKey, "score", 10, false);
            System.out.println("HIncrBy with replication result: " + hIncrByWithReplicationResult);
            
            // Hash decrement operations
            System.out.println("\nTesting hash decrement operations:");
            long hDecrByResult = client.hDecrBy(hashKey, "counter", 3);
            System.out.println("HDecrBy result: " + hDecrByResult);
            
            long hDecrByWithReplicationResult = client.hDecrBy(hashKey, "score", 5, false);
            System.out.println("HDecrBy with replication result: " + hDecrByWithReplicationResult);
            
            // Hash float increment
            System.out.println("\nTesting hash float increment operations:");
            client.hSet(hashKey, "rating", "4.5");
            
            double hIncrByFloatResult = client.hIncrByFloat(hashKey, "rating", 0.5);
            System.out.println("HIncrByFloat result: " + hIncrByFloatResult);
            
            double hIncrByFloatWithReplicationResult = client.hIncrByFloat(hashKey, "rating", 0.3, false);
            System.out.println("HIncrByFloat with replication result: " + hIncrByFloatWithReplicationResult);
            
            // Hash field deletion
            System.out.println("\nTesting hDel operations:");
            int hDelSingleResult = client.hDel(hashKey, "city");
            System.out.println("HDel single field result: " + hDelSingleResult);
            
            int hDelMultipleResult = client.hDel(hashKey, "department", "country", "timezone");
            System.out.println("HDel multiple fields result: " + hDelMultipleResult);
            
            // Hash scan operation
            System.out.println("\nTesting hScan operation:");
            HScanResult hScanResult = client.hScan(hashKey, 0);
            System.out.println("HScan result - cursor: " + hScanResult.getNextCursor() + 
                             ", fields: " + hScanResult.getFields());
            
            HScanResult hScanWithPatternResult = client.hScan(hashKey, 0, "*a*", 10);
            System.out.println("HScan with pattern result: " + hScanWithPatternResult.getFields());
            
            // Hash length
            System.out.println("\nTesting hLen operation:");
            int hLenResult = client.hLen(hashKey);
            System.out.println("HLen result: " + hLenResult);
            
            // Cleanup
            client.delMultiple(hashKey);
            
        } catch (Exception e) {
            System.err.println("Error in hash operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all set-related operations
     */
    private static void testSetOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Set Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            String setKey = "test:set:tags";

            // Add members to set
            System.out.println("Testing sAdd operation:");
            int sAddResult = client.sAdd(setKey, "redis", "cache", "database", "nosql");
            System.out.println("SAdd result: " + sAddResult);

            int sAddWithReplicationResult = client.sAdd(setKey, new String[]{"java", "spring"}, false);
            System.out.println("SAdd with replication result: " + sAddWithReplicationResult);

            // Get all set members
            System.out.println("\nTesting sMembers operation:");
            List<String> sMembersResult = client.sMembers(setKey);
            System.out.println("SMembers result: " + sMembersResult);

            // Add duplicate members (should return 0)
            System.out.println("\nTesting sAdd with duplicates:");
            int sAddDuplicateResult = client.sAdd(setKey, "redis", "cache");
            System.out.println("SAdd duplicate result: " + sAddDuplicateResult);

            // Cleanup
            client.delMultiple(setKey);

        } catch (Exception e) {
            System.err.println("Error in set operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test increment and decrement operations
     */
    private static void testIncrementDecrementOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Increment/Decrement Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            String counterKey = "test:counter";
            String floatKey = "test:float_counter";

            // Initialize counters
            client.set(counterKey, "10");
            client.set(floatKey, "5.5");

            // Increment operations
            System.out.println("Testing increment operations:");
            long incrByResult = client.incrBy(counterKey, 5);
            System.out.println("IncrBy result: " + incrByResult);

            long incrByWithReplicationResult = client.incrBy(counterKey, 3, false);
            System.out.println("IncrBy with replication result: " + incrByWithReplicationResult);

            // Decrement operations
            System.out.println("\nTesting decrement operations:");
            long decrByResult = client.decrBy(counterKey, 7);
            System.out.println("DecrBy result: " + decrByResult);

            long decrByWithReplicationResult = client.decrBy(counterKey, 2, false);
            System.out.println("DecrBy with replication result: " + decrByWithReplicationResult);

            // Float increment operations
            System.out.println("\nTesting float increment operations:");
            double incrByFloatResult = client.incrByFloat(floatKey, 2.5);
            System.out.println("IncrByFloat result: " + incrByFloatResult);

            double incrByFloatWithReplicationResult = client.incrByFloat(floatKey, 1.3, false);
            System.out.println("IncrByFloat with replication result: " + incrByFloatWithReplicationResult);

            // Cleanup
            client.delMultiple(counterKey, floatKey);

        } catch (Exception e) {
            System.err.println("Error in increment/decrement operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test script-related operations
     */
    private static void testScriptOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Script Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Load a simple Lua script
            System.out.println("Testing loadScript operation:");
            String script = "return 'Hello from RustyCluster Lua script!'";
            String scriptSha = client.loadScript(script);
            System.out.println("LoadScript result (SHA): " + scriptSha);

            if (scriptSha != null) {
                // Execute script using SHA
                System.out.println("\nTesting evalSha operation:");
                String evalShaResult = client.evalSha(scriptSha, new ArrayList<>(), new ArrayList<>());
                System.out.println("EvalSha result: " + evalShaResult);

                String evalShaWithReplicationResult = client.evalSha(scriptSha,
                                                                   new ArrayList<>(),
                                                                   new ArrayList<>(),
                                                                   false);
                System.out.println("EvalSha with replication result: " + evalShaWithReplicationResult);

                // Execute script with keys and args
                System.out.println("\nTesting evalSha with keys and args:");
                String scriptWithArgs = "return {KEYS[1], ARGV[1]}";
                String scriptWithArgsSha = client.loadScript(scriptWithArgs);

                if (scriptWithArgsSha != null) {
                    String evalShaWithArgsResult = client.evalSha(scriptWithArgsSha,
                                                                 Arrays.asList("mykey"),
                                                                 Arrays.asList("myarg"));
                    System.out.println("EvalSha with args result: " + evalShaWithArgsResult);
                }
            }

        } catch (Exception e) {
            System.err.println("Error in script operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test batch operations
     */
    private static void testBatchOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Batch Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Create batch operations (Note: This would require proper BatchOperation objects)
            System.out.println("Testing batchWrite operation:");
            System.out.println("Note: Batch operations require proper BatchOperation objects from the proto");
            System.out.println("This is a placeholder for batch operation testing");

            // In a real implementation, you would create BatchOperation objects like:
            // List<rustycluster.Rustycluster.BatchOperation> operations = new ArrayList<>();
            // operations.add(createSetOperation("key1", "value1"));
            // operations.add(createSetOperation("key2", "value2"));
            // List<Boolean> results = client.batchWrite(operations);
            // List<Boolean> resultsWithReplication = client.batchWrite(operations, false);

        } catch (Exception e) {
            System.err.println("Error in batch operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test utility operations
     */
    private static void testUtilityOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Utility Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Ping operation
            System.out.println("Testing ping operation:");
            boolean pingResult = client.ping();
            System.out.println("Ping result: " + pingResult);

            // Health check
            System.out.println("\nTesting healthCheck operation:");
            boolean healthResult = client.healthCheck();
            System.out.println("Health check result: " + healthResult);

        } catch (Exception e) {
            System.err.println("Error in utility operations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test all asynchronous operations
     */
    private static void testAsyncOperations(RustyClusterClientConfig config) {
        System.out.println("\n--- Testing Asynchronous Operations ---");

        try (RustyClusterClient client = new RustyClusterClient(config)) {
            // Async authentication
            System.out.println("Testing async authentication operations:");
            CompletableFuture<Boolean> authAsyncFuture = client.authenticateAsync();
            System.out.println("Async authentication result: " + authAsyncFuture.join());

            // Async string operations
            System.out.println("\nTesting async string operations:");

            CompletableFuture<Boolean> setAsyncFuture = client.setAsync("test:async:key1", "async_value1");
            CompletableFuture<Boolean> setAsyncWithReplicationFuture = client.setAsync("test:async:key2", "async_value2", false);

            // Wait for set operations to complete
            CompletableFuture.allOf(setAsyncFuture, setAsyncWithReplicationFuture).join();
            System.out.println("Async set result: " + setAsyncFuture.join());
            System.out.println("Async set with replication result: " + setAsyncWithReplicationFuture.join());

            // Async get operations
            CompletableFuture<String> getAsyncFuture = client.getAsync("test:async:key1");
            CompletableFuture<Boolean> existsAsyncFuture = client.existsAsync("test:async:key1");

            System.out.println("Async get result: " + getAsyncFuture.join());
            System.out.println("Async exists result: " + existsAsyncFuture.join());

            // Async setNX operation
            CompletableFuture<Boolean> setNXAsyncFuture = client.setNXAsync("test:async:key3", "async_value3");
            CompletableFuture<Boolean> setNXAsyncWithReplicationFuture = client.setNXAsync("test:async:key4", "async_value4", false);
            System.out.println("Async setNX result: " + setNXAsyncFuture.join());
            System.out.println("Async setNX with replication result: " + setNXAsyncWithReplicationFuture.join());

            // Async increment operations
            System.out.println("\nTesting async increment operations:");
            client.set("test:async:counter", "10");
            CompletableFuture<Long> incrByAsyncFuture = client.incrByAsync("test:async:counter", 5);
            CompletableFuture<Long> incrByAsyncWithReplicationFuture = client.incrByAsync("test:async:counter", 3, false);

            System.out.println("Async incrBy result: " + incrByAsyncFuture.join());
            System.out.println("Async incrBy with replication result: " + incrByAsyncWithReplicationFuture.join());

            // Async hash operations
            System.out.println("\nTesting async hash operations:");
            String asyncHashKey = "test:async:hash";

            CompletableFuture<Boolean> hSetAsyncFuture = client.hSetAsync(asyncHashKey, "field1", "value1");
            CompletableFuture<Boolean> hSetAsyncWithReplicationFuture = client.hSetAsync(asyncHashKey, "field2", "value2", false);

            CompletableFuture.allOf(hSetAsyncFuture, hSetAsyncWithReplicationFuture).join();
            System.out.println("Async hSet result: " + hSetAsyncFuture.join());
            System.out.println("Async hSet with replication result: " + hSetAsyncWithReplicationFuture.join());

            CompletableFuture<String> hGetAsyncFuture = client.hGetAsync(asyncHashKey, "field1");
            System.out.println("Async hGet result: " + hGetAsyncFuture.join());

            CompletableFuture<Map<String, String>> hGetAllAsyncFuture = client.hGetAllAsync(asyncHashKey);
            System.out.println("Async hGetAll result: " + hGetAllAsyncFuture.join());

            // Async hash multiple set
            Map<String, String> asyncHashFields = Map.of("field3", "value3", "field4", "value4");
            CompletableFuture<Boolean> hMSetAsyncFuture = client.hMSetAsync(asyncHashKey, asyncHashFields);
            CompletableFuture<Boolean> hMSetAsyncWithReplicationFuture = client.hMSetAsync(asyncHashKey,
                Map.of("field5", "value5"), false);

            System.out.println("Async hMSet result: " + hMSetAsyncFuture.join());
            System.out.println("Async hMSet with replication result: " + hMSetAsyncWithReplicationFuture.join());

            // Async hash exists
            CompletableFuture<Boolean> hExistsAsyncFuture = client.hExistsAsync(asyncHashKey, "field1");
            System.out.println("Async hExists result: " + hExistsAsyncFuture.join());

            // Async hash scan and length
            CompletableFuture<HScanResult> hScanAsyncFuture = client.hScanAsync(asyncHashKey, 0);
            CompletableFuture<HScanResult> hScanAsyncWithPatternFuture = client.hScanAsync(asyncHashKey, 0, "*1", 10);
            CompletableFuture<Integer> hLenAsyncFuture = client.hLenAsync(asyncHashKey);

            System.out.println("Async hScan result: " + hScanAsyncFuture.join());
            System.out.println("Async hScan with pattern result: " + hScanAsyncWithPatternFuture.join());
            System.out.println("Async hLen result: " + hLenAsyncFuture.join());

            // Async hash field deletion
            CompletableFuture<Integer> hDelAsyncFuture = client.hDelAsync(asyncHashKey, "field1", "field2");
            CompletableFuture<Integer> hDelAsyncWithReplicationFuture = client.hDelAsync(asyncHashKey,
                new String[]{"field3"}, false);

            System.out.println("Async hDel result: " + hDelAsyncFuture.join());
            System.out.println("Async hDel with replication result: " + hDelAsyncWithReplicationFuture.join());

            // Async set operations
            System.out.println("\nTesting async set operations:");
            String asyncSetKey = "test:async:set";

            CompletableFuture<Integer> sAddAsyncFuture = client.sAddAsync(asyncSetKey, "member1", "member2", "member3");
            CompletableFuture<Integer> sAddAsyncWithReplicationFuture = client.sAddAsync(asyncSetKey,
                new String[]{"member4", "member5"}, false);

            System.out.println("Async sAdd result: " + sAddAsyncFuture.join());
            System.out.println("Async sAdd with replication result: " + sAddAsyncWithReplicationFuture.join());

            CompletableFuture<List<String>> sMembersAsyncFuture = client.sMembersAsync(asyncSetKey);
            System.out.println("Async sMembers result: " + sMembersAsyncFuture.join());

            // Async script operations
            System.out.println("\nTesting async script operations:");
            String asyncScript = "return 'Async RustyCluster Lua script result!'";
            CompletableFuture<String> loadScriptAsyncFuture = client.loadScriptAsync(asyncScript);
            String asyncScriptSha = loadScriptAsyncFuture.join();
            System.out.println("Async loadScript result: " + asyncScriptSha);

            if (asyncScriptSha != null) {
                CompletableFuture<String> evalShaAsyncFuture = client.evalShaAsync(asyncScriptSha,
                                                                                  new ArrayList<>(),
                                                                                  new ArrayList<>());
                CompletableFuture<String> evalShaAsyncWithReplicationFuture = client.evalShaAsync(asyncScriptSha,
                                                                                                 new ArrayList<>(),
                                                                                                 new ArrayList<>(),
                                                                                                 false);

                System.out.println("Async evalSha result: " + evalShaAsyncFuture.join());
                System.out.println("Async evalSha with replication result: " + evalShaAsyncWithReplicationFuture.join());
            }

            // Async utility operations
            System.out.println("\nTesting async utility operations:");
            CompletableFuture<Boolean> pingAsyncFuture = client.pingAsync();
            CompletableFuture<Boolean> healthCheckAsyncFuture = client.healthCheckAsync();

            System.out.println("Async ping result: " + pingAsyncFuture.join());
            System.out.println("Async health check result: " + healthCheckAsyncFuture.join());

            // Async cleanup
            System.out.println("\nTesting async cleanup operations:");
            CompletableFuture<Integer> delMultipleAsyncFuture = client.delMultipleAsync(
                "test:async:key1", "test:async:key2", "test:async:key3", "test:async:key4",
                "test:async:counter", asyncHashKey, asyncSetKey);
            CompletableFuture<Integer> delMultipleAsyncWithReplicationFuture = client.delMultipleAsync(
                new String[]{"test:async:cleanup"}, false);

            System.out.println("Async delMultiple result: " + delMultipleAsyncFuture.join());
            System.out.println("Async delMultiple with replication result: " + delMultipleAsyncWithReplicationFuture.join());

            CompletableFuture<Boolean> deleteAsyncFuture = client.deleteAsync("test:async:single");
            CompletableFuture<Boolean> deleteAsyncWithReplicationFuture = client.deleteAsync("test:async:single2", false);

            System.out.println("Async delete result: " + deleteAsyncFuture.join());
            System.out.println("Async delete with replication result: " + deleteAsyncWithReplicationFuture.join());

            System.out.println("All async operations completed successfully!");

        } catch (Exception e) {
            System.err.println("Error in async operations: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
